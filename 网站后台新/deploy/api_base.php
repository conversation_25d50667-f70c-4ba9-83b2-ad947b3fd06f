<?php
/**
 * API基础类
 * 提供安全验证、JWT认证、加密/解密等通用功能
 * 
 * @version 2.0.0
 */
class ApiBase {
    private $jwt_secret;
    private $encryption_key;
    
    public function __construct() {
        // 初始化JWT密钥（从环境变量或配置中获取）
        $this->initializeJwtSecret();
        
        // 初始化加密密钥
        $this->initializeEncryptionKey();
    }
    
    /**
     * 初始化JWT密钥
     */
    private function initializeJwtSecret() {
        require_once dirname(__DIR__) . '/api/env_manager.php';
        
        // 获取环境变量
        $env_manager = EnvManager::getInstance();
        $env_manager->load();
        
        // 从环境变量获取JWT密钥
        $this->jwt_secret = $env_manager->get('API_JWT_SECRET');
        
        // 如果没有JWT密钥，则生成一个并保存
        if (empty($this->jwt_secret)) {
            $this->jwt_secret = $this->generateRandomString(64);
            $env_manager->set('API_JWT_SECRET', $this->jwt_secret);
            $env_manager->save();
        }
    }
    
    /**
     * 初始化加密密钥
     */
    private function initializeEncryptionKey() {
        require_once dirname(__DIR__) . '/api/env_manager.php';
        
        // 获取环境变量
        $env_manager = EnvManager::getInstance();
        $env_manager->load();
        
        // 从环境变量获取加密密钥
        $this->encryption_key = $env_manager->get('API_ENCRYPTION_KEY');
        
        // 如果没有加密密钥，则生成一个并保存
        if (empty($this->encryption_key)) {
            $this->encryption_key = $this->generateRandomString(32);
            $env_manager->set('API_ENCRYPTION_KEY', $this->encryption_key);
            $env_manager->save();
        }
    }
    
    /**
     * 生成随机字符串
     */
    private function generateRandomString($length = 32) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[random_int(0, $charactersLength - 1)];
        }
        
        return $randomString;
    }
    
    /**
     * 记录API调用日志
     * 
     * @param string $key 卡密
     * @param string $ip IP地址
     * @param string $endpoint API端点
     * @param string $status 状态 (success, error, info, warning)
     * @param string $message 日志消息
     * @return bool 是否成功记录
     */
    public function logApiCall($key, $ip, $endpoint, $status, $message) {
        global $pdo;
        
        try {
            // 检查日志表是否存在
            $tableExists = false;
            
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE 'api_logs'");
                $tableExists = ($stmt->rowCount() > 0);
            } catch (Exception $e) {
                // 表不存在或其他错误
            }
            
            // 如果表不存在，则创建
            if (!$tableExists) {
                $pdo->exec("
                    CREATE TABLE api_logs (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        license_key VARCHAR(255) NOT NULL,
                        ip_address VARCHAR(45) NOT NULL,
                        endpoint VARCHAR(50) NOT NULL,
                        status VARCHAR(20) NOT NULL,
                        message TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ");
            }
            
            // 记录日志
            $stmt = $pdo->prepare("
                INSERT INTO api_logs 
                    (license_key, ip_address, endpoint, status, message, created_at) 
                VALUES 
                    (:key, :ip, :endpoint, :status, :message, NOW())
            ");
            
            return $stmt->execute([
                ':key' => $key,
                ':ip' => $ip,
                ':endpoint' => $endpoint,
                ':status' => $status,
                ':message' => $message
            ]);
        } catch (Exception $e) {
            error_log("API日志记录失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 根据卡密获取许可证信息
     * 
     * @param string $key 卡密
     * @return array|false 许可证信息或false
     */
    public function getLicenseByKey($key) {
        global $pdo;
        
        try {
            $stmt = $pdo->prepare("SELECT * FROM license_keys WHERE key_value = :key");
            $stmt->execute([':key' => $key]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("获取卡密信息失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 解析请求数据（处理加密和JSON请求）
     * 
     * @param string $request_body 请求体
     * @return array|null 解析后的请求数据
     */
    public function parseRequest($request_body) {
        try {
            // 解析JSON请求
            $data = json_decode($request_body, true);
            
            // 检查是否是加密的请求
            if (isset($data['encrypted']) && $data['encrypted'] === true && !empty($data['data'])) {
                // 解密数据
                return $this->decryptData($data['data']);
            }
            
            return $data;
        } catch (Exception $e) {
            error_log("解析请求失败: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 解密数据
     * 
     * @param string $encryptedData 加密的数据
     * @return array|null 解密后的数据
     */
    public function decryptData($encryptedData) {
        try {
            // 简单解密实现（实际项目中应使用更安全的加密方式）
            $data = urldecode(base64_decode($encryptedData));
            return json_decode($data, true);
        } catch (Exception $e) {
            error_log("解密数据失败: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 加密响应数据
     * 
     * @param array $data 响应数据
     * @return string 加密后的JSON响应
     */
    public function encryptResponse($data) {
        try {
            // 添加时间戳
            $data['timestamp'] = time() * 1000;
            
            // 检查是否需要加密
            if (isset($_SERVER['HTTP_X_API_VERSION']) && version_compare($_SERVER['HTTP_X_API_VERSION'], 'v2', '>=')) {
                // 简单加密实现（实际项目中应使用更安全的加密方式）
                $jsonData = json_encode($data);
                $encryptedData = base64_encode(urlencode($jsonData));
                
                return json_encode([
                    'encrypted' => true,
                    'version' => 'v2',
                    'data' => $encryptedData
                ]);
            }
            
            // 不加密直接返回
            return json_encode($data);
        } catch (Exception $e) {
            error_log("加密响应失败: " . $e->getMessage());
            return json_encode(['success' => false, 'message' => '服务器内部错误']);
        }
    }
    
    /**
     * 生成安全令牌
     * 
     * @param string $key 卡密
     * @param int $license_id 许可证ID
     * @return string 安全令牌
     */
    public function generateSecurityToken($key, $license_id) {
        // 生成JWT格式的安全令牌
        $header = [
            'alg' => 'HS256',
            'typ' => 'JWT'
        ];
        
        $payload = [
            'sub' => $license_id,
            'key' => substr(md5($key), 0, 10), // 只保存卡密的哈希部分
            'iat' => time(),
            'exp' => time() + (24 * 60 * 60), // 24小时有效期
            'jti' => $this->generateRandomString(16)
        ];
        
        // 编码头部和负载
        $header_encoded = $this->base64UrlEncode(json_encode($header));
        $payload_encoded = $this->base64UrlEncode(json_encode($payload));
        
        // 生成签名
        $signature = hash_hmac('sha256', $header_encoded . '.' . $payload_encoded, $this->jwt_secret, true);
        $signature_encoded = $this->base64UrlEncode($signature);
        
        // 返回完整的JWT
        return $header_encoded . '.' . $payload_encoded . '.' . $signature_encoded;
    }
    
    /**
     * 验证安全令牌
     * 
     * @param string $key 卡密
     * @param string $token 安全令牌
     * @return bool 令牌是否有效
     */
    public function validateSecurityToken($key, $token) {
        try {
            // 拆分JWT
            $parts = explode('.', $token);
            if (count($parts) != 3) {
                return false;
            }
            
            list($header_encoded, $payload_encoded, $signature_encoded) = $parts;
            
            // 验证签名
            $signature = $this->base64UrlDecode($signature_encoded);
            $expected_signature = hash_hmac('sha256', $header_encoded . '.' . $payload_encoded, $this->jwt_secret, true);
            
            if (!hash_equals($signature, $expected_signature)) {
                return false;
            }
            
            // 解析负载
            $payload = json_decode($this->base64UrlDecode($payload_encoded), true);
            
            // 验证过期时间
            if (!isset($payload['exp']) || $payload['exp'] < time()) {
                return false;
            }
            
            // 验证卡密
            if (!isset($payload['key']) || substr(md5($key), 0, 10) !== $payload['key']) {
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            error_log("验证安全令牌失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Base64 URL安全编码
     */
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    /**
     * Base64 URL安全解码
     */
    private function base64UrlDecode($data) {
        $padded = str_pad($data, strlen($data) % 4, '=', STR_PAD_RIGHT);
        return base64_decode(strtr($padded, '-_', '+/'));
    }
} 