<?php
/**
 * 环境变量管理系统
 * 
 * 提供安全的方式存储和访问数据库凭证等敏感信息
 * 使API系统在数据库账号密码变更时不受影响
 */

class EnvManager {
    private static $instance = null;
    private $env_cache = [];
    private $env_file = null;
    private $env_fallback_file = null;
    private $loaded = false;
    
    /**
     * 获取EnvManager单例实例
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数，设置环境变量文件路径
     */
    private function __construct() {
        // 主环境变量文件（在web目录外，更安全）
        $this->env_file = dirname(__DIR__, 2) . '/.env';
        
        // 备用环境变量文件（在web目录内，但通过.htaccess保护）
        $this->env_fallback_file = dirname(__DIR__) . '/.env';
        
        // 确保备用环境变量目录中有.htaccess文件保护
        $this->ensureHtaccessProtection(dirname($this->env_fallback_file));
    }
    
    /**
     * 确保目录中有.htaccess保护
     */
    private function ensureHtaccessProtection($directory) {
        $htaccess_file = $directory . '/.htaccess';
        
        if (!file_exists($htaccess_file)) {
            $htaccess_content = "# 防止环境变量文件被直接访问\n";
            $htaccess_content .= "Order deny,allow\n";
            $htaccess_content .= "Deny from all\n";
            $htaccess_content .= "# 防止.env文件被直接访问\n";
            $htaccess_content .= "<Files ~ \"\.env$\">\n";
            $htaccess_content .= "    Order deny,allow\n";
            $htaccess_content .= "    Deny from all\n";
            $htaccess_content .= "</Files>\n";
            
            // 尝试创建.htaccess文件
            @file_put_contents($htaccess_file, $htaccess_content);
        }
    }
    
    /**
     * 加载环境变量
     */
    public function load() {
        if ($this->loaded) {
            return true;
        }
        
        // 首先尝试从系统环境变量加载
        $this->loadFromSystemEnv();
        
        // 然后尝试从环境变量文件加载
        if (file_exists($this->env_file)) {
            $this->loadFromFile($this->env_file);
        } elseif (file_exists($this->env_fallback_file)) {
            $this->loadFromFile($this->env_fallback_file);
        }
        
        // 如果环境变量不存在，尝试从现有配置提取
        if (empty($this->env_cache['DB_HOST'])) {
            $this->loadFromExistingConfig();
        }
        
        $this->loaded = true;
        return true;
    }
    
    /**
     * 从系统环境变量加载
     */
    private function loadFromSystemEnv() {
        // 数据库连接信息
        $this->env_cache['DB_HOST'] = getenv('DB_HOST') ?: null;
        $this->env_cache['DB_PORT'] = getenv('DB_PORT') ?: null;
        $this->env_cache['DB_NAME'] = getenv('DB_NAME') ?: null;
        $this->env_cache['DB_USER'] = getenv('DB_USER') ?: null;
        $this->env_cache['DB_PASS'] = getenv('DB_PASS') ?: null;
        
        // API配置
        $this->env_cache['API_JWT_SECRET'] = getenv('API_JWT_SECRET') ?: null;
        $this->env_cache['API_RATE_LIMIT'] = getenv('API_RATE_LIMIT') ?: null;
    }
    
    /**
     * 从.env文件加载环境变量
     */
    private function loadFromFile($file) {
        $env_content = file_get_contents($file);
        $lines = explode("\n", $env_content);
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // 跳过注释和空行
            if (empty($line) || strpos($line, '#') === 0) {
                continue;
            }
            
            // 解析KEY=VALUE格式
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // 移除引号
                if (strpos($value, '"') === 0 && strrpos($value, '"') === strlen($value) - 1) {
                    $value = substr($value, 1, -1);
                }
                if (strpos($value, "'") === 0 && strrpos($value, "'") === strlen($value) - 1) {
                    $value = substr($value, 1, -1);
                }
                
                $this->env_cache[$key] = $value;
            }
        }
    }
    
    /**
     * 从现有配置文件中提取数据库信息
     */
    private function loadFromExistingConfig() {
        $db_file = dirname(__DIR__) . '/includes/db.php';
        
        if (file_exists($db_file)) {
            $content = file_get_contents($db_file);
            
            // 提取数据库连接信息
            if (preg_match('/\$db_host\s*=\s*["\'](.*?)["\']/i', $content, $matches)) {
                $this->env_cache['DB_HOST'] = $matches[1];
            }
            
            if (preg_match('/\$db_name\s*=\s*["\'](.*?)["\']/i', $content, $matches)) {
                $this->env_cache['DB_NAME'] = $matches[1];
            }
            
            if (preg_match('/\$db_user\s*=\s*["\'](.*?)["\']/i', $content, $matches)) {
                $this->env_cache['DB_USER'] = $matches[1];
            }
            
            if (preg_match('/\$db_pass\s*=\s*["\'](.*?)["\']/i', $content, $matches)) {
                $this->env_cache['DB_PASS'] = $matches[1];
            }
            
            if (preg_match('/\$db_port\s*=\s*["\'](.*?)["\']/i', $content, $matches)) {
                $this->env_cache['DB_PORT'] = $matches[1];
            }
        }
        
        // 从API配置中提取JWT密钥
        try {
            require_once dirname(__DIR__) . '/includes/db.php';
            if (isset($pdo)) {
                $stmt = $pdo->query("SELECT config_value FROM system_config WHERE config_key = 'api_security_config'");
                if ($stmt && $row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $config = json_decode($row['config_value'], true);
                    if (isset($config['layer1']['jwtSecret'])) {
                        $this->env_cache['API_JWT_SECRET'] = $config['layer1']['jwtSecret'];
                    }
                    if (isset($config['layer1']['rateLimit'])) {
                        $this->env_cache['API_RATE_LIMIT'] = $config['layer1']['rateLimit'];
                    }
                }
            }
        } catch (Exception $e) {
            // 忽略错误
        }
    }
    
    /**
     * 获取环境变量值
     * 
     * @param string $key 环境变量名
     * @param mixed $default 默认值
     * @return mixed 环境变量值
     */
    public function get($key, $default = null) {
        if (!$this->loaded) {
            $this->load();
        }
        
        return $this->env_cache[$key] ?? $default;
    }
    
    /**
     * 设置环境变量值（仅在内存中）
     * 
     * @param string $key 环境变量名
     * @param mixed $value 环境变量值
     */
    public function set($key, $value) {
        if (!$this->loaded) {
            $this->load();
        }
        
        $this->env_cache[$key] = $value;
    }
    
    /**
     * 保存环境变量到文件
     * 
     * @return bool 是否成功保存
     */
    public function save() {
        if (!$this->loaded) {
            $this->load();
        }
        
        $env_content = "# 自动生成的环境变量配置文件\n";
        $env_content .= "# 最后更新: " . date('Y-m-d H:i:s') . "\n\n";
        
        foreach ($this->env_cache as $key => $value) {
            if (is_string($value) && strpos($value, ' ') !== false) {
                $env_content .= "$key=\"$value\"\n";
            } else {
                $env_content .= "$key=$value\n";
            }
        }
        
        $target_file = file_exists(dirname($this->env_file)) ? $this->env_file : $this->env_fallback_file;
        
        // 确保目标目录存在
        $dir = dirname($target_file);
        if (!file_exists($dir)) {
            if (!mkdir($dir, 0755, true)) {
                return false;
            }
        }
        
        return file_put_contents($target_file, $env_content) !== false;
    }
    
    /**
     * 获取数据库连接参数
     * 
     * @return array 数据库连接参数
     */
    public function getDatabaseConfig() {
        if (!$this->loaded) {
            $this->load();
        }
        
        return [
            'host' => $this->get('DB_HOST', 'localhost'),
            'port' => $this->get('DB_PORT', '3306'),
            'dbname' => $this->get('DB_NAME', ''),
            'user' => $this->get('DB_USER', ''),
            'pass' => $this->get('DB_PASS', ''),
        ];
    }
    
    /**
     * 将当前数据库配置保存到环境变量
     * 
     * @param array $config 数据库配置参数
     * @return bool 是否成功保存
     */
    public function saveDatabaseConfig($config) {
        if (!$this->loaded) {
            $this->load();
        }
        
        $this->set('DB_HOST', $config['host'] ?? 'localhost');
        $this->set('DB_PORT', $config['port'] ?? '3306');
        $this->set('DB_NAME', $config['dbname'] ?? '');
        $this->set('DB_USER', $config['user'] ?? '');
        $this->set('DB_PASS', $config['pass'] ?? '');
        
        return $this->save();
    }
}

// 提供便捷的全局访问函数
function env($key, $default = null) {
    return EnvManager::getInstance()->get($key, $default);
} 