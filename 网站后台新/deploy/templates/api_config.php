<?php
/**
 * API接口配置管理页面 - 模板文件
 * 只包含页面内容，不包含HTML结构
 */

// 严格的错误控制
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 0);
set_time_limit(30);

// 辅助函数
function getIntervalText($seconds) {
    if ($seconds < 3600) {
        return floor($seconds / 60) . '分钟';
    } elseif ($seconds < 86400) {
        return floor($seconds / 3600) . '小时';
    } elseif ($seconds < 2592000) {
        return floor($seconds / 86400) . '天';
    } else {
        return floor($seconds / 2592000) . '个月';
    }
}

// 数据库连接优化
function getOptimizedDB() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_SILENT,
                PDO::ATTR_TIMEOUT => 3,
                PDO::ATTR_PERSISTENT => false
            ];
            
            // 智能路径检测
            $possible_paths = [
                __DIR__ . '/../../database.db',
                dirname(dirname(__DIR__)) . '/database.db',
                $_SERVER['DOCUMENT_ROOT'] . '/database.db',
                'database.db',
                '/www/wwwroot/www.xiaomeihuakefu.cn/database.db'
            ];
            
            $db_path = null;
            foreach ($possible_paths as $path) {
                if (file_exists($path) && is_readable($path)) {
                    $db_path = $path;
                    break;
                }
            }
            
            if ($db_path) {
                $pdo = new PDO('sqlite:' . $db_path, null, null, $options);
            } else {
                $pdo = false;
            }
        } catch (Exception $e) {
            $pdo = false;
        }
    }
    
    return $pdo;
}

// 引入API密钥管理器
require_once '../api/key_manager.php';

// 初始化API密钥管理器
$keyManager = new ApiKeyManager(getOptimizedDB());

// AJAX请求处理
if (isset($_POST['ajax']) && $_POST['ajax'] === '1') {
    // 清除任何之前的输出
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');
    
    $response = ['success' => false, 'message' => '', 'data' => ''];
    
    try {
        $pdo = getOptimizedDB();
        if (!$pdo) {
            throw new Exception('数据库连接失败');
        }
        
        $action = $_POST['action'] ?? '';
        
        if ($action === 'generate_api_key') {
            // 引入自动初始化令牌管理器
            require_once '../api/auto_init_token_manager.php';
            
            $keyName = $_POST['key_name'] ?? 'API密钥 ' . date('Y-m-d H:i:s');
            $autoRefresh = isset($_POST['auto_refresh']) && $_POST['auto_refresh'];
            $refreshInterval = (int)($_POST['refresh_interval'] ?? 604800);
            
            // 使用唯一令牌生成功能
            $options = [
                'key_name' => $keyName,
                'auto_refresh' => $autoRefresh,
                'refresh_interval' => $refreshInterval
            ];
            
            $result = generateUniqueToken($pdo, 'api_key', $options);
            
            if ($result['success']) {
                $response['success'] = true;
                $response['message'] = 'API密钥生成成功！旧密钥已自动失效，刷新间隔设置为 ' . getIntervalText($refreshInterval);
                $response['data'] = [
                    'id' => $result['id'],
                    'name' => $keyName,
                    'key' => substr($result['token'], 0, 16) . '...',
                    'time' => date('Y-m-d H:i:s')
                ];
            } else {
                throw new Exception($result['error']);
            }
        }
        
        if ($action === 'generate_jwt_secret') {
            // 引入自动初始化令牌管理器
            require_once '../api/auto_init_token_manager.php';
            
            $autoRefresh = isset($_POST['auto_refresh']) && $_POST['auto_refresh'];
            $refreshInterval = (int)($_POST['refresh_interval'] ?? 604800);
            
            // 使用唯一令牌生成功能
            $options = [
                'auto_refresh' => $autoRefresh,
                'refresh_interval' => $refreshInterval
            ];
            
            $result = generateUniqueToken($pdo, 'jwt_secret', $options);
            
            if ($result['success']) {
                $response['success'] = true;
                $response['message'] = 'JWT令牌生成成功！旧令牌已自动失效，刷新间隔设置为 ' . getIntervalText($refreshInterval);
                $response['data'] = [
                    'id' => $result['id'],
                    'secret' => substr($result['token'], 0, 16) . '...',
                    'time' => date('Y-m-d H:i:s')
                ];
            } else {
                throw new Exception($result['error']);
            }
        }
        
        if ($action === 'update_refresh_interval') {
            $tokenType = $_POST['token_type'] ?? '';
            $refreshInterval = (int)($_POST['refresh_interval'] ?? 0);
            
            if ($tokenType && $refreshInterval > 0) {
                // 检查表是否存在
                $tableExists = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='token_management'")->fetch();
                
                if ($tableExists) {
                    $stmt = $pdo->prepare('UPDATE token_management SET refresh_interval = ?, expires_at = datetime("now", "+" || ? || " seconds") WHERE token_type = ? AND is_active = 1');
                    $result = $stmt->execute([$refreshInterval, $refreshInterval, $tokenType]);
                    
                    if ($result) {
                        $intervalText = getIntervalText($refreshInterval);
                        $typeText = $tokenType === 'api_key' ? 'API密钥' : 'JWT令牌';
                        $response['success'] = true;
                        $response['message'] = $typeText . '刷新间隔已更新为 ' . $intervalText;
                    } else {
                        $response['message'] = '刷新间隔更新失败';
                    }
                } else {
                    $response['message'] = '令牌管理表不存在';
                }
            } else {
                $response['message'] = '参数无效：类型=' . $tokenType . ', 间隔=' . $refreshInterval;
            }
        }
        
        if ($action === 'get_active_tokens') {
            // 检查表是否存在
            $tableExists = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='token_management'")->fetch();
            
            if ($tableExists) {
                $stmt = $pdo->prepare('SELECT id, token_type, expires_at, auto_refresh, created_at FROM token_management WHERE is_active = 1 ORDER BY id DESC LIMIT 5');
                $stmt->execute();
                $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // 在PHP中处理状态
                foreach ($tokens as &$token) {
                    if ($token['expires_at'] === null) {
                        $token['expires_in'] = '永不过期';
                        $token['status_color'] = 'success';
                    } elseif (strtotime($token['expires_at']) > time()) {
                        $token['expires_in'] = '有效';
                        $token['status_color'] = 'success';
                    } else {
                        $token['expires_in'] = '已过期';
                        $token['status_color'] = 'danger';
                    }
                }
                
                $response['success'] = true;
                $response['data'] = $tokens;
            } else {
                $response['success'] = true;
                $response['data'] = [];
            }
        }
        
        // 新增：设置API密钥自动更新
        if ($action === 'set_auto_update') {
            $tokenId = (int)($_POST['token_id'] ?? 0);
            $tokenType = $_POST['token_type'] ?? '';
            $autoUpdate = isset($_POST['auto_update']) && $_POST['auto_update'] == 1;
            $updateInterval = (int)($_POST['update_interval'] ?? 604800);
            
            if ($tokenId && $tokenType) {
                $result = $keyManager->setAutoUpdate($tokenId, $tokenType, $autoUpdate, $updateInterval);
                
                if ($result['success']) {
                    $response['success'] = true;
                    $response['message'] = $result['message'];
                    $response['data'] = $result['data'];
                } else {
                    $response['message'] = $result['message'];
                }
            } else {
                $response['message'] = '参数无效';
            }
        }
        
        // 新增：获取自动更新设置
        if ($action === 'get_auto_update_settings') {
            $tokenId = isset($_POST['token_id']) ? (int)$_POST['token_id'] : null;
            
            $result = $keyManager->getAutoUpdateSettings($tokenId);
            
            if ($result['success']) {
                $response['success'] = true;
                $response['data'] = $result['data'];
            } else {
                $response['message'] = $result['message'];
            }
        }
        
        // 新增：立即执行自动更新
        if ($action === 'run_auto_update') {
            $result = $keyManager->runAutoUpdate();
            
            if ($result['success']) {
                $response['success'] = true;
                $response['message'] = $result['message'];
                $response['data'] = $result['details'];
            } else {
                $response['message'] = $result['message'];
            }
        }
        
    } catch (Exception $e) {
        $response['message'] = '操作失败: ' . $e->getMessage();
    }
    
    // 确保输出纯净的JSON
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 页面加载时不执行任何数据库查询，避免卡顿
?>

<style>
    .api-config-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .api-config-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
    }
    
    .api-config-header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        color: white;
    }
    
    .api-config-header p {
        color: rgba(255, 255, 255, 0.8);
        font-size: 16px;
    }
    
    .api-glass-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    .api-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .api-form-group {
        margin-bottom: 15px;
    }
    
    .api-form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
    }
    
    .api-form-group input, .api-form-group select {
        width: 100%;
        padding: 12px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        color: white;
        font-size: 14px;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }
    
    .api-form-group input:focus, .api-form-group select:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.4);
        background: rgba(255, 255, 255, 0.15);
    }
    
    .api-checkbox-group {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
    }
    
    .api-checkbox-group input[type="checkbox"] {
        width: auto;
        margin: 0;
    }
    
    .api-btn {
        width: 100%;
        padding: 14px;
        border: none;
        border-radius: 10px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        font-size: 15px;
        transition: all 0.3s ease;
        position: relative;
    }
    
    .api-btn-primary {
        background: linear-gradient(45deg, #28a745, #20c997);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .api-btn-secondary {
        background: linear-gradient(45deg, #007bff, #6610f2);
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }
    
    .api-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
    
    .api-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
    
    .api-loading {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    
    .api-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }
    
    .api-alert {
        padding: 15px;
        border-radius: 10px;
        margin: 15px 0;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .api-alert-success {
        background: rgba(40, 167, 69, 0.2);
        border: 1px solid rgba(40, 167, 69, 0.3);
        color: #28a745;
    }
    
    .api-alert-error {
        background: rgba(220, 53, 69, 0.2);
        border: 1px solid rgba(220, 53, 69, 0.3);
        color: #dc3545;
    }
    
    .api-token-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }
    
    .api-token-table th,
    .api-token-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: white;
    }
    
    .api-token-table th {
        background: rgba(255, 255, 255, 0.1);
        font-weight: 600;
    }
    
    .api-status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }
    
    .api-status-success {
        background: rgba(40, 167, 69, 0.2);
        color: #28a745;
    }
    
    .api-status-danger {
        background: rgba(220, 53, 69, 0.2);
        color: #dc3545;
    }
    
    @media (max-width: 768px) {
        .api-grid {
            grid-template-columns: 1fr;
        }
        
        .api-config-container {
            padding: 15px;
        }
        
        .api-config-header h1 {
            font-size: 2rem;
        }
    }
</style>

<div class="api-config-container">
    <div class="api-config-header">
        <h1><i class="fas fa-cogs"></i> API接口配置</h1>
        <p>管理API密钥、安全设置和系统配置</p>
    </div>

    <div id="api-message-container"></div>

    <div class="api-grid">
        <!-- API密钥管理 -->
        <div class="api-glass-card">
            <h3 style="margin-bottom: 15px; color: white;">
                <i class="fas fa-key"></i> API密钥管理
            </h3>
            <form id="api-form">
                <div class="api-form-group">
                    <label>密钥名称</label>
                    <input type="text" id="api-name" value="<?php echo 'API密钥 ' . date('Y-m-d H:i:s'); ?>" required>
                </div>
                
                <div class="api-checkbox-group">
                    <input type="checkbox" id="api-auto-refresh" checked>
                    <label for="api-auto-refresh">启用自动刷新</label>
                </div>
                
                <div class="api-form-group">
                    <label>刷新间隔</label>
                    <select id="api-refresh-interval">
                        <option value="86400">1天</option>
                        <option value="604800" selected>7天</option>
                        <option value="2592000">30天</option>
                        <option value="5184000">60天</option>
                        <option value="15552000">180天</option>
                    </select>
                </div>
                
                <button type="submit" class="api-btn api-btn-primary" id="api-btn">
                    <span class="btn-text"><i class="fas fa-plus"></i> 生成新密钥</span>
                    <div class="api-loading">
                        <div class="api-spinner"></div>
                    </div>
                </button>
            </form>
        </div>

        <!-- JWT令牌管理 -->
        <div class="api-glass-card">
            <h3 style="margin-bottom: 15px; color: white;">
                <i class="fas fa-shield-alt"></i> JWT令牌管理
            </h3>
            <form id="jwt-form">
                <div class="api-form-group">
                    <label>令牌名称</label>
                    <input type="text" value="<?php echo 'JWT令牌 ' . date('Y-m-d H:i:s'); ?>" readonly>
                </div>
                
                <div class="api-checkbox-group">
                    <input type="checkbox" id="jwt-auto-refresh" checked>
                    <label for="jwt-auto-refresh">启用自动刷新</label>
                </div>
                
                <div class="api-form-group">
                    <label>刷新间隔</label>
                    <select id="jwt-refresh-interval">
                        <option value="3600">1小时</option>
                        <option value="86400">1天</option>
                        <option value="604800" selected>7天</option>
                        <option value="2592000">30天</option>
                    </select>
                </div>
                
                <button type="submit" class="api-btn api-btn-secondary" id="jwt-btn">
                    <span class="btn-text"><i class="fas fa-plus"></i> 生成新JWT密钥</span>
                    <div class="api-loading">
                        <div class="api-spinner"></div>
                    </div>
                </button>
            </form>
        </div>
    </div>

    <!-- 活动令牌状态 -->
    <div class="api-glass-card">
        <h3 style="margin-bottom: 15px; color: white;">
            <i class="fas fa-list"></i> 活动令牌状态
        </h3>
        <div style="margin-bottom: 15px; display: flex; gap: 10px;">
            <button type="button" class="api-btn api-btn-secondary" id="refresh-tokens-btn" onclick="refreshTokenList()" style="width: auto; padding: 8px 16px; font-size: 14px;">
                <i class="fas fa-sync-alt"></i> 刷新状态
            </button>
            <button type="button" class="api-btn api-btn-primary" id="run-auto-update-btn" onclick="runAutoUpdate()" style="width: auto; padding: 8px 16px; font-size: 14px;">
                <i class="fas fa-bolt"></i> 立即执行自动更新
            </button>
        </div>
        <div id="token-list">
            <p style="text-align: center; color: rgba(255, 255, 255, 0.7); padding: 20px;">
                点击"刷新状态"查看活动令牌
            </p>
        </div>
    </div>
    
    <!-- API密钥自动更新设置 -->
    <div class="api-glass-card">
        <h3 style="margin-bottom: 15px; color: white;">
            <i class="fas fa-clock"></i> API密钥自动更新设置
        </h3>
        <p style="color: white; margin-bottom: 20px;">
            设置API密钥自动更新的时间间隔，系统将在指定时间后自动重新生成密钥。
        </p>
        
        <div id="auto-update-settings">
            <!-- 这里将通过JavaScript动态加载设置 -->
            <p style="text-align: center; color: rgba(255, 255, 255, 0.7); padding: 20px;">
                点击"刷新状态"查看令牌后，可以设置自动更新
            </p>
        </div>
    </div>
</div>

<script>
// API配置页面的JavaScript代码
document.addEventListener('DOMContentLoaded', function() {
    console.log('API配置页面加载完成');
    
    // 绑定表单事件
    const apiForm = document.getElementById('api-form');
    const jwtForm = document.getElementById('jwt-form');
    
    if (apiForm) {
        apiForm.addEventListener('submit', handleApiFormSubmit);
    }
    
    if (jwtForm) {
        jwtForm.addEventListener('submit', handleJwtFormSubmit);
    }
});

// 显示消息
function showApiMessage(message, type = 'success') {
    const container = document.getElementById('api-message-container');
    if (!container) return;
    
    const alertClass = type === 'success' ? 'api-alert-success' : 'api-alert-error';
    const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
    
    container.innerHTML = `
        <div class="api-alert ${alertClass}">
            <i class="${icon}"></i>
            ${message}
        </div>
    `;
    
    // 3秒后自动隐藏
    setTimeout(() => {
        container.innerHTML = '';
    }, 3000);
}

// 设置按钮加载状态
function setApiButtonLoading(buttonId, loading) {
    const button = document.getElementById(buttonId);
    if (!button) return;
    
    const btnText = button.querySelector('.btn-text');
    const loadingSpinner = button.querySelector('.api-loading');
    
    if (loading) {
        button.disabled = true;
        if (btnText) btnText.style.visibility = 'hidden';
        if (loadingSpinner) loadingSpinner.style.display = 'block';
    } else {
        button.disabled = false;
        if (btnText) btnText.style.visibility = 'visible';
        if (loadingSpinner) loadingSpinner.style.display = 'none';
    }
}

// 发送AJAX请求
async function sendApiRequest(data) {
    try {
        const response = await fetch(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                ...data,
                ajax: '1'
            })
        });
        
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('AJAX请求失败:', error);
        return { success: false, message: '网络请求失败: ' + error.message };
    }
}

// 处理API密钥表单提交
async function handleApiFormSubmit(e) {
    e.preventDefault();
    
    const keyName = document.getElementById('api-name').value.trim();
    const autoRefresh = document.getElementById('api-auto-refresh').checked;
    const refreshInterval = document.getElementById('api-refresh-interval').value;
    
    if (!keyName) {
        showApiMessage('请输入密钥名称', 'error');
        return;
    }
    
    setApiButtonLoading('api-btn', true);
    
    const result = await sendApiRequest({
        action: 'generate_api_key',
        key_name: keyName,
        auto_refresh: autoRefresh,
        refresh_interval: refreshInterval
    });
    
    setApiButtonLoading('api-btn', false);
    
    if (result.success) {
        showApiMessage(result.message, 'success');
        // 更新密钥名称
        document.getElementById('api-name').value = 'API密钥 ' + new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        // 自动刷新令牌列表
        setTimeout(() => {
            refreshTokenList();
        }, 500);
    } else {
        showApiMessage(result.message, 'error');
    }
}

// 处理JWT令牌表单提交
async function handleJwtFormSubmit(e) {
    e.preventDefault();
    
    const autoRefresh = document.getElementById('jwt-auto-refresh').checked;
    const refreshInterval = document.getElementById('jwt-refresh-interval').value;
    
    setApiButtonLoading('jwt-btn', true);
    
    const result = await sendApiRequest({
        action: 'generate_jwt_secret',
        auto_refresh: autoRefresh,
        refresh_interval: refreshInterval
    });
    
    setApiButtonLoading('jwt-btn', false);
    
    if (result.success) {
        showApiMessage(result.message, 'success');
        // 自动刷新令牌列表
        setTimeout(() => {
            refreshTokenList();
        }, 500);
    } else {
        showApiMessage(result.message, 'error');
    }
}

// 刷新令牌列表
async function refreshTokenList() {
    const container = document.getElementById('token-list');
    if (!container) return;
    
    container.innerHTML = '<p style="text-align: center; color: rgba(255, 255, 255, 0.7); padding: 20px;"><i class="fas fa-spinner fa-spin"></i> 加载中...</p>';
    
    const result = await sendApiRequest({
        action: 'get_active_tokens'
    });
    
    if (result.success && result.data) {
        if (result.data.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: rgba(255, 255, 255, 0.7); padding: 20px;">暂无活动令牌</p>';
        } else {
            let tableHTML = `
                <table class="api-token-table">
                    <thead>
                        <tr>
                            <th>类型</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>过期状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            result.data.forEach(token => {
                const typeText = token.token_type === 'api_key' ? 'API密钥' : 'JWT令牌';
                const statusClass = token.status_color === 'success' ? 'api-status-success' : 'api-status-danger';
                
                tableHTML += `
                    <tr>
                        <td>${typeText}</td>
                        <td><span class="api-status-badge ${statusClass}">${token.expires_in}</span></td>
                        <td>${new Date(token.created_at).toLocaleString('zh-CN')}</td>
                        <td>${token.auto_refresh ? '自动刷新' : '手动管理'}</td>
                        <td>
                            <button type="button" class="api-btn" style="width: auto; padding: 4px 8px; font-size: 12px;" 
                                onclick="showAutoUpdateSettings(${token.id}, '${token.token_type}')">
                                设置自动更新
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            tableHTML += '</tbody></table>';
            container.innerHTML = tableHTML;
        }
    } else {
        container.innerHTML = '<p style="text-align: center; color: rgba(255, 255, 255, 0.7); padding: 20px;">加载失败，请重试</p>';
    }
}

// 显示自动更新设置
async function showAutoUpdateSettings(tokenId, tokenType) {
    const container = document.getElementById('auto-update-settings');
    if (!container) return;
    
    container.innerHTML = '<p style="text-align: center; color: rgba(255, 255, 255, 0.7); padding: 20px;"><i class="fas fa-spinner fa-spin"></i> 加载中...</p>';
    
    // 获取当前设置
    const result = await sendApiRequest({
        action: 'get_auto_update_settings',
        token_id: tokenId
    });
    
    let formHTML = `
        <form id="auto-update-form">
            <input type="hidden" id="auto-update-token-id" value="${tokenId}">
            <input type="hidden" id="auto-update-token-type" value="${tokenType}">
            
            <div class="api-form-group">
                <label>令牌类型</label>
                <input type="text" value="${tokenType === 'api_key' ? 'API密钥' : 'JWT令牌'}" readonly>
            </div>
            
            <div class="api-checkbox-group">
                <input type="checkbox" id="auto-update-enabled" ${result.success && result.data && result.data.auto_update ? 'checked' : ''}>
                <label for="auto-update-enabled">启用自动更新</label>
            </div>
            
            <div class="api-form-group">
                <label>更新间隔</label>
                <select id="auto-update-interval">
                    <option value="86400">1天</option>
                    <option value="604800">7天</option>
                    <option value="2592000">30天</option>
                    <option value="5184000">60天</option>
                    <option value="15552000">180天</option>
                </select>
            </div>
            
            <button type="submit" class="api-btn api-btn-primary" id="save-auto-update-btn">
                <span class="btn-text"><i class="fas fa-save"></i> 保存设置</span>
                <div class="api-loading">
                    <div class="api-spinner"></div>
                </div>
            </button>
        </form>
    `;
    
    container.innerHTML = formHTML;
    
    // 设置当前值
    if (result.success && result.data) {
        document.getElementById('auto-update-interval').value = result.data.update_interval;
    }
    
    // 绑定表单提交事件
    const form = document.getElementById('auto-update-form');
    if (form) {
        form.addEventListener('submit', handleAutoUpdateFormSubmit);
    }
}

// 处理自动更新表单提交
async function handleAutoUpdateFormSubmit(e) {
    e.preventDefault();
    
    const tokenId = document.getElementById('auto-update-token-id').value;
    const tokenType = document.getElementById('auto-update-token-type').value;
    const autoUpdate = document.getElementById('auto-update-enabled').checked ? 1 : 0;
    const updateInterval = document.getElementById('auto-update-interval').value;
    
    setApiButtonLoading('save-auto-update-btn', true);
    
    const result = await sendApiRequest({
        action: 'set_auto_update',
        token_id: tokenId,
        token_type: tokenType,
        auto_update: autoUpdate,
        update_interval: updateInterval
    });
    
    setApiButtonLoading('save-auto-update-btn', false);
    
    if (result.success) {
        showApiMessage(result.message, 'success');
        // 刷新令牌列表
        setTimeout(() => {
            refreshTokenList();
        }, 500);
    } else {
        showApiMessage(result.message, 'error');
    }
}

// 立即执行自动更新
async function runAutoUpdate() {
    setApiButtonLoading('run-auto-update-btn', true);
    
    const result = await sendApiRequest({
        action: 'run_auto_update'
    });
    
    setApiButtonLoading('run-auto-update-btn', false);
    
    if (result.success) {
        showApiMessage(result.message, 'success');
        // 刷新令牌列表
        setTimeout(() => {
            refreshTokenList();
        }, 500);
    } else {
        showApiMessage(result.message, 'error');
    }
}
</script> 