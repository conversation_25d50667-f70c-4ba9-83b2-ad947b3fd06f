<?php
// 最最简单的API页面 - 去除所有卡顿因素

// 立即输出缓冲
ob_start();

// 关闭所有可能的延迟
ini_set('max_execution_time', 5);
ini_set('memory_limit', '32M');
error_reporting(0);
ini_set('display_errors', 0);

$msg = '';

// 最简单的数据库操作
if ($_POST) {
    try {
        $db = new PDO('sqlite:../../database.db');
        $db->setAttribute(PDO::ATTR_TIMEOUT, 2);
        
        if (isset($_POST['gen_api'])) {
            $name = 'API-' . date('His');
            $key = bin2hex(random_bytes(4));
            $sql = "INSERT INTO api_keys (key_name, api_key, is_active, created_by) VALUES (?, ?, 1, 1)";
            $stmt = $db->prepare($sql);
            $result = $stmt->execute([$name, $key]);
            $msg = $result ? "API密钥生成成功: $name" : "API密钥生成失败";
        }
        
        if (isset($_POST['gen_jwt'])) {
            $token = bin2hex(random_bytes(8));
            $sql = "INSERT INTO token_management (token_type, token_value, is_active) VALUES ('jwt_secret', ?, 1)";
            $stmt = $db->prepare($sql);
            $result = $stmt->execute([$token]);
            $msg = $result ? "JWT令牌生成成功" : "JWT令牌生成失败";
        }
        
        $db = null; // 立即关闭连接
    } catch (Exception $e) {
        $msg = "错误: " . $e->getMessage();
    }
}

// 立即输出
ob_end_flush();
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>最简API</title>
<style>
body{margin:20px;font-family:Arial;}
.box{border:1px solid #ccc;padding:15px;margin:10px 0;border-radius:5px;}
.btn{padding:8px 16px;background:#007bff;color:white;border:none;border-radius:3px;cursor:pointer;}
.btn:hover{background:#0056b3;}
.msg{padding:8px;margin:8px 0;border-radius:3px;}
.ok{background:#d4edda;color:#155724;}
.err{background:#f8d7da;color:#721c24;}
</style>
</head>
<body>

<h2>🔧 最简API测试</h2>

<?php if($msg): ?>
<div class="msg <?php echo strpos($msg, '成功') !== false ? 'ok' : 'err'; ?>">
<?php echo htmlspecialchars($msg); ?>
</div>
<?php endif; ?>

<div class="box">
<h3>🔑 API密钥</h3>
<form method="post">
<button type="submit" name="gen_api" class="btn">生成API密钥</button>
</form>
</div>

<div class="box">
<h3>🛡️ JWT令牌</h3>
<form method="post">
<button type="submit" name="gen_jwt" class="btn">生成JWT令牌</button>
</form>
</div>

<div class="box">
<h3>📋 最新记录</h3>
<?php
try {
    $db = new PDO('sqlite:../../database.db');
    $db->setAttribute(PDO::ATTR_TIMEOUT, 1);
    
    // API密钥
    $stmt = $db->query("SELECT key_name, created_at FROM api_keys ORDER BY id DESC LIMIT 2");
    if ($stmt) {
        echo "<h4>API密钥:</h4>";
        while ($row = $stmt->fetch()) {
            echo "<div>" . htmlspecialchars($row['key_name']) . " - " . date('H:i:s', strtotime($row['created_at'])) . "</div>";
        }
    }
    
    // JWT令牌
    $stmt = $db->query("SELECT created_at FROM token_management WHERE token_type='jwt_secret' ORDER BY id DESC LIMIT 2");
    if ($stmt) {
        echo "<h4>JWT令牌:</h4>";
        while ($row = $stmt->fetch()) {
            echo "<div>JWT密钥 - " . date('H:i:s', strtotime($row['created_at'])) . "</div>";
        }
    }
    
    $db = null;
} catch (Exception $e) {
    echo "<div class='err'>数据获取失败: " . htmlspecialchars($e->getMessage()) . "</div>";
}
?>
</div>

<p style="color:#666;font-size:12px;text-align:center;">最简测试版 - 排查卡顿问题</p>

</body>
</html> 