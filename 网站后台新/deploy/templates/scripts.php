<?php
// 不要在这里设置header，因为index.php已经输出了内容
$message = '';

// 脚本处理函数 - 优化版：保留完整代码，不自动清理头部信息
function processUserScript($code) {
    $result = [
        'name' => '',
        'version' => '1.0.0',
        'code' => $code  // 保留完整的原始代码
    ];

    // 检查是否包含userscript头部
    if (strpos($code, '// ==UserScript==') !== false) {
        // 提取脚本名称
        if (preg_match('/@name\s+(.+)/i', $code, $matches)) {
            $result['name'] = trim($matches[1]);
        }

        // 提取版本号
        if (preg_match('/@version\s+(.+)/i', $code, $matches)) {
            $result['version'] = trim($matches[1]);
        }

        // 【优化】不再自动清理头部信息，保留完整代码
        // 原来的代码会自动截取从 (function() { 开始的部分
        // 现在改为保留完整的脚本代码，包括 UserScript 头部信息
        $result['code'] = $code;  // 保持原始代码不变
    }

    return $result;
}

// 处理AJAX状态切换请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'toggle_script_status') {
    header('Content-Type: application/json');
    
    // 确保session已启动
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    // 检查用户是否已登录
    if (!isset($_SESSION['admin_user_id'])) {
        echo json_encode(['success' => false, 'message' => '用户未登录']);
        exit;
    }
    
    $script_id = intval($_POST['script_id'] ?? 0);
    $new_status = $_POST['new_status'] ?? '';
    
    if ($script_id > 0 && in_array($new_status, ['active', 'inactive'])) {
        try {
            // 确保scripts表有status字段
            $pdo->exec("ALTER TABLE scripts ADD COLUMN IF NOT EXISTS status ENUM('active','inactive') DEFAULT 'active'");
            
            $stmt = $pdo->prepare("UPDATE scripts SET status = ? WHERE id = ?");
            if ($stmt->execute([$new_status, $script_id])) {
                echo json_encode(['success' => true, 'message' => '状态更新成功']);
            } else {
                $error_info = $stmt->errorInfo();
                echo json_encode(['success' => false, 'message' => '数据库更新失败: ' . $error_info[2]]);
            }
        } catch (Exception $e) {
            // 如果是字段不存在的错误，尝试添加字段
            if (strpos($e->getMessage(), 'status') !== false || strpos($e->getMessage(), 'Unknown column') !== false) {
                try {
                    $pdo->exec("ALTER TABLE scripts ADD COLUMN status ENUM('active','inactive') DEFAULT 'active'");
                    $stmt = $pdo->prepare("UPDATE scripts SET status = ? WHERE id = ?");
                    if ($stmt->execute([$new_status, $script_id])) {
                        echo json_encode(['success' => true, 'message' => '状态更新成功']);
                    } else {
                        $error_info = $stmt->errorInfo();
                        echo json_encode(['success' => false, 'message' => '数据库更新失败: ' . $error_info[2]]);
                    }
                } catch (Exception $e2) {
                    echo json_encode(['success' => false, 'message' => '更新失败：' . $e2->getMessage()]);
                }
            } else {
                echo json_encode(['success' => false, 'message' => '更新失败：' . $e->getMessage()]);
            }
        }
    } else {
        echo json_encode(['success' => false, 'message' => '参数错误：script_id=' . $script_id . ', new_status=' . $new_status]);
    }
    exit;
}



// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_script'])) {
        $script_id = $_POST['script_id'] ?? null;
        $name = trim($_POST['name']);
        $version = trim($_POST['version']);
        $description = trim($_POST['description'] ?? '');
        $code = $_POST['script_code'];

        // 权限设置 - 修复：正确处理隐藏输入框的值
        $has_wechat_store = (isset($_POST['has_wechat_store']) && $_POST['has_wechat_store'] == '1') ? 1 : 0;
        $has_douyin_store = (isset($_POST['has_douyin_store']) && $_POST['has_douyin_store'] == '1') ? 1 : 0;

        // 如果名称为空，尝试自动处理脚本
        if (empty($name) && !empty($code)) {
            $processed = processUserScript($code);
            $name = $processed['name'];
            $version = $processed['version'];
            $code = $processed['code'];
        }

        // 验证必填字段
        if (empty($name) || empty($version) || empty($code)) {
            $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 脚本名称、版本和代码都不能为空</div>";
        } else {
            // 检查表结构，确定可用的字段
            $table_columns = [];
            try {
                $stmt_check = $pdo->query("DESCRIBE scripts");
                $columns = $stmt_check->fetchAll();
                foreach ($columns as $col) {
                    $table_columns[] = $col['Field'];
                }
            } catch (Exception $e) {
                // 如果无法获取表结构，使用基本字段
                $table_columns = ['id', 'name', 'version', 'description', 'created_at'];
            }

            // 构建动态SQL语句
            $update_fields = ['name = ?', 'version = ?', 'description = ?'];
            $insert_fields = ['name', 'version', 'description'];
            $insert_placeholders = ['?', '?', '?'];
            $params = [$name, $version, $description];

            // 添加代码字段 - 如果两个字段都存在，都要更新以保持同步
            if (in_array('script_code', $table_columns)) {
                $update_fields[] = 'script_code = ?';
                $insert_fields[] = 'script_code';
                $insert_placeholders[] = '?';
                $params[] = $code;
            }
            if (in_array('code', $table_columns)) {
                $update_fields[] = 'code = ?';
                $insert_fields[] = 'code';
                $insert_placeholders[] = '?';
                $params[] = $code;
            }

            // 添加权限字段
            if (in_array('has_wechat_store', $table_columns)) {
                $update_fields[] = 'has_wechat_store = ?';
                $insert_fields[] = 'has_wechat_store';
                $insert_placeholders[] = '?';
                $params[] = $has_wechat_store;
            }
            if (in_array('has_douyin_store', $table_columns)) {
                $update_fields[] = 'has_douyin_store = ?';
                $insert_fields[] = 'has_douyin_store';
                $insert_placeholders[] = '?';
                $params[] = $has_douyin_store;
            }

            if ($script_id) { // 更新
                $update_sql = "UPDATE scripts SET " . implode(', ', $update_fields) . " WHERE id = ?";
                $update_params = array_merge($params, [$script_id]);

                $stmt = $pdo->prepare($update_sql);
                if ($stmt->execute($update_params)) {
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已成功更新！</div>";
                } else {
                    $message = "<div class='message error'><i class='fas fa-times-circle'></i> 更新脚本失败</div>";
                }
            } else { // 新增
                $insert_sql = "INSERT INTO scripts (" . implode(', ', $insert_fields) . ") VALUES (" . implode(', ', $insert_placeholders) . ")";

                $stmt = $pdo->prepare($insert_sql);
                if ($stmt->execute($params)) {
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已成功添加！</div>";
                } else {
                    $message = "<div class='message error'><i class='fas fa-times-circle'></i> 添加脚本失败</div>";
                }
            }
        }
    }
}

// 删除脚本
if (isset($_GET['delete_script'])) {
    $script_id = $_GET['delete_script'];
    
    // 检查是否有卡密关联此脚本
    $stmt_check = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE script_id = ?");
    $stmt_check->execute([$script_id]);
    $linked_keys = $stmt_check->fetchColumn();
    
    if ($linked_keys > 0) {
        $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 无法删除：还有 {$linked_keys} 个卡密关联此脚本</div>";
    } else {
        $stmt = $pdo->prepare("DELETE FROM scripts WHERE id = ?");
        if ($stmt->execute([$script_id])) {
            $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已删除！</div>";
        }
    }
}

// 获取要编辑的脚本
$edit_script = null;
if (isset($_GET['edit_script'])) {
    $stmt = $pdo->prepare("SELECT * FROM scripts WHERE id = ?");
    $stmt->execute([$_GET['edit_script']]);
    $edit_script = $stmt->fetch(PDO::FETCH_ASSOC);
}

// 获取脚本列表 - 包含状态字段和权限字段，兼容不同的数据库结构
// 首先检查表结构，确定可用的字段
$table_columns = [];
try {
    $stmt = $pdo->query("DESCRIBE scripts");
    $columns = $stmt->fetchAll();
    foreach ($columns as $col) {
        $table_columns[] = $col['Field'];
    }
} catch (Exception $e) {
    // 如果无法获取表结构，使用基本字段
    $table_columns = ['id', 'name', 'version', 'description', 'created_at'];
}

// 根据可用字段构建查询
$script_code_field = 'NULL as script_code';
if (in_array('script_code', $table_columns)) {
    $script_code_field = 's.script_code';
} elseif (in_array('code', $table_columns)) {
    $script_code_field = 's.code as script_code';
}

$has_wechat_store_field = in_array('has_wechat_store', $table_columns) ? 's.has_wechat_store' : '0 as has_wechat_store';
$has_douyin_store_field = in_array('has_douyin_store', $table_columns) ? 's.has_douyin_store' : '0 as has_douyin_store';
$status_field = in_array('status', $table_columns) ? 's.status' : "'active' as status";

$scripts_sql = "
    SELECT s.id,
           s.name,
           s.version,
           s.description,
           {$script_code_field},
           {$status_field},
           {$has_wechat_store_field},
           {$has_douyin_store_field},
           s.created_at,
           (SELECT COUNT(*) FROM license_keys WHERE script_id = s.id) as linked_keys_count
    FROM scripts s
    ORDER BY s.id DESC
";
$scripts = $pdo->query($scripts_sql)->fetchAll(PDO::FETCH_ASSOC);

// 确保每个脚本都有默认值
foreach ($scripts as &$script) {
    $script['name'] = $script['name'] ?? '';
    $script['version'] = $script['version'] ?? '1.0.0';
    $script['description'] = $script['description'] ?? '';
    $script['status'] = $script['status'] ?? 'active'; // 默认启用
    $script['has_wechat_store'] = $script['has_wechat_store'] ?? 0;
    $script['has_douyin_store'] = $script['has_douyin_store'] ?? 0;
    $script['linked_keys_count'] = isset($script['linked_keys_count']) ? (int)$script['linked_keys_count'] : 0;
    $script['created_at'] = $script['created_at'] ?? date('Y-m-d H:i:s');
}
?>

<style>
    /* 脚本管理页面专用样式 */
    .scripts-container {
        max-width: 100%;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
    }
    
    .card {
        background: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(20px) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 15px !important;
        padding: 20px !important;
        margin-bottom: 20px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    }
    
    .card h2 {
        color: white !important;
        margin-bottom: 20px !important;
        font-size: 18px !important;
        font-weight: 600 !important;
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
    }
    
    .form-inline {
        display: flex !important;
        flex-direction: column !important;
        gap: 0 !important;
        margin-bottom: 20px !important;
    }
    
    @media (max-width: 768px) {
        .form-inline {
            flex-direction: column !important;
        }
    }
    
    .form-group {
        margin-bottom: 20px !important;
    }
    
    .form-group label {
        display: block !important;
        color: white !important;
        margin-bottom: 8px !important;
        font-weight: 500 !important;
        font-size: 14px !important;
    }
    
    .form-group input,
    .form-group textarea {
        width: 100% !important;
        padding: 12px 15px !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 8px !important;
        color: white !important;
        font-size: 14px !important;
        transition: all 0.3s ease !important;
    }
    
    .form-group input::placeholder,
    .form-group textarea::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
    }
    
    .form-group input:focus,
    .form-group textarea:focus {
        outline: none !important;
        border-color: #ff6b9d !important;
        box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2) !important;
    }
    
    .table-container {
        overflow-x: auto !important;
        border-radius: 8px !important;
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }
    
    table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 14px !important;
    }
    
    table th {
        background: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
        padding: 15px 12px !important;
        text-align: left !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        white-space: nowrap !important;
    }
    
    table td {
        padding: 15px 12px !important;
        color: rgba(255, 255, 255, 0.9) !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        vertical-align: middle !important;
        font-size: 13px !important;
    }
    
    table tr:hover {
        background: rgba(255, 255, 255, 0.05) !important;
    }
    
    .status-badge {
        display: inline-block !important;
        padding: 6px 12px !important;
        border-radius: 12px !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
    }
    
    .status-active {
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        border: 1px solid rgba(255, 255, 255, 0.5) !important;
    }
    
    .status-disabled {
        background: rgba(220, 53, 69, 0.3) !important;
        color: #dc3545 !important;
    }
    
    .btn {
        display: inline-flex !important;
        align-items: center !important;
        gap: 8px !important;
        padding: 10px 20px !important;
        border: none !important;
        border-radius: 8px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-decoration: none !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #ff6b9d, #c44569) !important;
        color: white !important;
    }
    
    .btn-secondary {
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
    }
    
    .btn-success {
        background: linear-gradient(135deg, #4ade80, #22c55e) !important;
        color: white !important;
    }
    
    .btn-warning {
        background: linear-gradient(135deg, #fbbf24, #f59e0b) !important;
        color: white !important;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #f87171, #ef4444) !important;
        color: white !important;
    }
    
    .btn:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    }
    
    .message {
        padding: 15px 20px !important;
        border-radius: 8px !important;
        margin-bottom: 20px !important;
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
    }
    
    .message.success {
        background: rgba(34, 197, 94, 0.2) !important;
        border: 1px solid rgba(34, 197, 94, 0.3) !important;
        color: #4ade80 !important;
    }
    
    .message.error {
        background: rgba(239, 68, 68, 0.2) !important;
        border: 1px solid rgba(239, 68, 68, 0.3) !important;
        color: #f87171 !important;
    }
    
    .script-description {
        font-size: 12px !important;
        opacity: 0.7 !important;
        margin-top: 3px !important;
        line-height: 1.3 !important;
    }
    
    .script-name {
        font-weight: 600 !important;
        color: white !important;
        margin-bottom: 3px !important;
        font-size: 14px !important;
    }
    
    .action-buttons {
        display: flex !important;
        gap: 8px !important;
        justify-content: center !important;
        flex-wrap: wrap !important;
    }
    
    .action-buttons .btn {
        padding: 8px 12px !important;
        font-size: 12px !important;
        min-width: 36px !important;
    }
    
    .auto-process-info {
        background: rgba(52, 152, 219, 0.2) !important;
        border: 1px solid rgba(52, 152, 219, 0.3) !important;
        color: white !important;
        padding: 15px 20px !important;
        border-radius: 8px !important;
        margin-bottom: 20px !important;
        font-size: 14px !important;
    }

    /* 全新的权限卡片样式 */
    .permission-cards-wrapper {
        display: flex;
        gap: 20px;
        margin-top: 15px;
        flex-wrap: wrap;
    }

    .permission-card-item {
        width: 180px;
        height: 180px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        z-index: 10;
        pointer-events: auto;
    }

    .permission-card-box {
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        transition: all 0.3s ease;
        pointer-events: auto;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    .permission-card-item:hover .permission-card-box {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
    }

    .permission-card-item.selected .permission-card-box {
        background: rgba(138, 43, 226, 0.3);
        border-color: rgba(138, 43, 226, 0.6);
        box-shadow: 0 0 20px rgba(138, 43, 226, 0.4);
    }

    .permission-card-title {
        color: white;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        line-height: 1.2;
        pointer-events: none;
        user-select: none;
    }

    .permission-card-subtitle {
        color: white;
        font-size: 16px;
        font-weight: 500;
        opacity: 0.9;
        line-height: 1.2;
        pointer-events: none;
        user-select: none;
    }

    /* 权限徽章样式 */
    .permission-badges {
        display: flex !important;
        flex-direction: column !important;
        gap: 4px !important;
        align-items: center !important;
    }

    .permission-badge {
        display: inline-flex !important;
        align-items: center !important;
        gap: 4px !important;
        padding: 2px 8px !important;
        border-radius: 12px !important;
        font-size: 11px !important;
        font-weight: 500 !important;
        white-space: nowrap !important;
        color: white !important;
    }

    .permission-badge.wechat-store {
        background: linear-gradient(135deg, #07c160, #00a854) !important;
        border: 1px solid rgba(7, 193, 96, 0.3) !important;
    }

    .permission-badge.douyin-store {
        background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
        border: 1px solid rgba(255, 107, 53, 0.3) !important;
    }

    .permission-badge i {
        font-size: 10px !important;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .permission-cards-wrapper {
            flex-direction: column;
            align-items: center;
        }

        .permission-card-item {
            width: 160px;
            height: 160px;
        }

        .permission-badges {
            flex-direction: row !important;
            flex-wrap: wrap !important;
            justify-content: center !important;
        }

        .permission-badge {
            font-size: 10px !important;
            padding: 1px 6px !important;
        }
    }
</style>

<div class="scripts-container">
    <div class="card">
        <h2><i class="fas fa-plus-circle"></i> <?php echo $edit_script ? '编辑脚本' : '添加脚本'; ?></h2>
        
        <div class="auto-process-info">
            <i class="fas fa-info-circle"></i>
            智能处理：粘贴完整的UserScript代码，系统将自动提取脚本名称、版本号，<strong>保留完整的头部信息</strong>
        </div>
        
        <?php echo $message; ?>
        
        <form method="POST" id="scriptForm">
            <?php if ($edit_script): ?>
                <input type="hidden" name="script_id" value="<?php echo $edit_script['id']; ?>">
            <?php endif; ?>
            
            <div class="form-inline">
                <div class="form-group">
                    <label><i class="fas fa-tag"></i> 脚本名称</label>
                    <input type="text" name="name" id="scriptName" value="<?php echo htmlspecialchars($edit_script['name'] ?? '', ENT_QUOTES, 'UTF-8'); ?>" placeholder="例如：小梅花AI智能客服">
                </div>
                <div class="form-group">
                    <label><i class="fas fa-code-branch"></i> 版本号</label>
                    <input type="text" name="version" id="scriptVersion" value="<?php echo htmlspecialchars($edit_script['version'] ?? '', ENT_QUOTES, 'UTF-8'); ?>" placeholder="例如：1.0.0">
                </div>
            </div>
            
            <div class="form-group">
                <label><i class="fas fa-info-circle"></i> 脚本描述 (可选)</label>
                <input type="text" name="description" value="<?php echo htmlspecialchars($edit_script['description'] ?? '', ENT_QUOTES, 'UTF-8'); ?>" placeholder="简单描述脚本功能">
            </div>

            <!-- 功能权限设置 -->
            <div class="form-group">
                <label><i class="fas fa-cogs"></i> 功能权限</label>

                <!-- 版本标识 -->
                <div style="background: red; color: white; padding: 10px; margin: 10px 0; text-align: center; font-weight: bold;">
                    ✅ DEPLOY版本 - 复选框已修复 - 请刷新页面
                </div>

                <!-- 简单直接的复选框版本 -->
                <div style="display: flex; gap: 20px; margin-top: 15px; flex-wrap: wrap;">
                    <label style="display: flex; align-items: center; gap: 15px; cursor: pointer; color: white; font-size: 16px; margin: 0; padding: 20px; background: rgba(255, 255, 255, 0.1); border-radius: 15px; border: 2px solid rgba(255, 255, 255, 0.3); transition: all 0.3s ease; min-width: 250px; justify-content: center;" id="wechat_label">
                        <input type="checkbox" name="has_wechat_store" value="1" id="wechat_checkbox" <?php echo ($edit_script['has_wechat_store'] ?? 0) ? 'checked' : ''; ?> style="margin: 0; transform: scale(1.5);" onchange="updateWechatLabel()">
                        <span style="font-weight: 600;">
                            <i class="fas fa-store" style="margin-right: 8px;"></i>
                            小梅花AI客服-微信小店
                        </span>
                    </label>

                    <label style="display: flex; align-items: center; gap: 15px; cursor: pointer; color: white; font-size: 16px; margin: 0; padding: 20px; background: rgba(255, 255, 255, 0.1); border-radius: 15px; border: 2px solid rgba(255, 255, 255, 0.3); transition: all 0.3s ease; min-width: 250px; justify-content: center;" id="douyin_label">
                        <input type="checkbox" name="has_douyin_store" value="1" id="douyin_checkbox" <?php echo ($edit_script['has_douyin_store'] ?? 0) ? 'checked' : ''; ?> style="margin: 0; transform: scale(1.5);" onchange="updateDouyinLabel()">
                        <span style="font-weight: 600;">
                            <i class="fas fa-shopping-cart" style="margin-right: 8px;"></i>
                            小梅花AI客服-抖店
                        </span>
                    </label>
                </div>

                <!-- 测试按钮 -->
                <div style="margin-top: 15px; text-align: center;">
                    <button type="button" onclick="testPermissions()" style="padding: 10px 20px; background: #333; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        测试权限选择功能
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label><i class="fas fa-code"></i> 脚本代码</label>
                <textarea name="script_code" id="scriptCode" rows="10" required placeholder="在此粘贴您的JavaScript代码或完整的UserScript..."><?php echo htmlspecialchars($edit_script['script_code'] ?? '', ENT_QUOTES, 'UTF-8'); ?></textarea>
            </div>
            
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-top: 10px;">
                <button type="submit" name="save_script" class="btn btn-primary">
                    <i class="fas fa-save"></i> <?php echo $edit_script ? '更新脚本' : '保存脚本'; ?>
                </button>
                
                <button type="button" onclick="processScript()" class="btn btn-success">
                    <i class="fas fa-magic"></i> 提取信息
                </button>
                
                <?php if ($edit_script): ?>
                    <a href="index.php?page=scripts" class="btn btn-secondary">
                        <i class="fas fa-times"></i> 取消编辑
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <div class="card">
        <h2><i class="fas fa-list"></i> 脚本管理</h2>
        
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 15px;">
            <div style="color: rgba(255,255,255,0.8); font-size: 14px;">
                <i class="fas fa-info-circle"></i> 
                脚本列表 (共 <?php echo count($scripts); ?> 个脚本)
            </div>
            

        </div>
        
        <?php if (empty($scripts)): ?>
            <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 20px;">
                <i class="fas fa-info-circle"></i> 暂无脚本数据，请先添加脚本
            </p>
        <?php else: ?>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th style="width: 50px; text-align: center;"><i class="fas fa-hashtag"></i> ID</th>
                            <th><i class="fas fa-tag"></i> 脚本信息</th>
                            <th style="width: 100px; text-align: center;"><i class="fas fa-code-branch"></i> 版本</th>
                            <th style="width: 120px; text-align: center;"><i class="fas fa-shield-alt"></i> 功能权限</th>
                            <th style="width: 80px; text-align: center;"><i class="fas fa-link"></i> 关联</th>
                            <th style="width: 100px; text-align: center;"><i class="fas fa-toggle-on"></i> 状态</th>
                            <th style="width: 120px;"><i class="fas fa-calendar"></i> 创建时间</th>
                            <th style="width: 150px; text-align: center;"><i class="fas fa-cogs"></i> 操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($scripts as $script): ?>
                        <tr>
                            <td style="text-align: center; font-weight: bold;"><?php echo $script['id']; ?></td>
                            <td>
                                <div class="script-name"><?php echo htmlspecialchars($script['name'], ENT_QUOTES, 'UTF-8'); ?></div>
                                <?php if (!empty($script['description'])): ?>
                                    <div class="script-description"><?php echo htmlspecialchars($script['description'], ENT_QUOTES, 'UTF-8'); ?></div>
                                <?php endif; ?>
                            </td>
                            <td style="text-align: center;">
                                <span class="status-badge"><?php echo htmlspecialchars($script['version'], ENT_QUOTES, 'UTF-8'); ?></span>
                            </td>
                            <td style="text-align: center;">
                                <div class="permission-badges">
                                    <?php if ($script['has_wechat_store']): ?>
                                        <span class="permission-badge wechat-store" title="支持微信小店">
                                            <i class="fas fa-store"></i> 微信小店
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($script['has_douyin_store']): ?>
                                        <span class="permission-badge douyin-store" title="支持抖店">
                                            <i class="fas fa-shopping-cart"></i> 抖店
                                        </span>
                                    <?php endif; ?>
                                    <?php if (!$script['has_wechat_store'] && !$script['has_douyin_store']): ?>
                                        <span style="opacity: 0.5; font-size: 12px;">无权限</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td style="text-align: center;">
                                <?php if ($script['linked_keys_count'] > 0): ?>
                                    <span class="status-badge status-active"><?php echo $script['linked_keys_count']; ?></span>
                                <?php else: ?>
                                    <span style="opacity: 0.5;">0</span>
                                <?php endif; ?>
                            </td>
                            <td style="text-align: center;">
                                <span class="status-badge <?php echo $script['status'] === 'active' ? 'status-active' : 'status-disabled'; ?>"
                                      style="cursor: pointer;"
                                      onclick="toggleStatus(<?php echo $script['id']; ?>)"
                                      title="点击切换状态">
                                    <?php echo $script['status'] === 'active' ? '启用' : '禁用'; ?>
                                </span>
            </td>
                            <td>
                                <?php echo date('Y-m-d H:i', strtotime($script['created_at'])); ?>
                            </td>
                            <td style="text-align: center;">
                                <div class="action-buttons">
                                    <a href="index.php?page=scripts&edit_script=<?php echo $script['id']; ?>"
                                       class="btn btn-secondary"
                                       title="编辑">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>

                                    <a href="index.php?page=scripts&delete_script=<?php echo $script['id']; ?>"
                                       class="btn btn-danger"
                                       title="删除"
                                       onclick="return confirm('确定要删除这个脚本吗？<?php echo $script['linked_keys_count'] > 0 ? '\\n注意：此脚本关联了 ' . $script['linked_keys_count'] . ' 个卡密！' : ''; ?>')">
                                        <i class="fas fa-trash"></i> 删除
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>


</div>

<script>
function processScript() {
    const codeTextarea = document.getElementById('scriptCode');
    const nameInput = document.getElementById('scriptName');
    const versionInput = document.getElementById('scriptVersion');

    let code = codeTextarea.value;

    console.log('processScript 被调用');
    console.log('脚本代码长度:', code.length);

    if (!code.trim()) {
        console.log('脚本代码为空，返回');
        return; // 静默返回，不显示警告
    }

    // 提取脚本名称 - 改进正则表达式，支持更多格式
    const nameMatch = code.match(/@name\s+(.+?)(?:\r?\n|$)/i);
    console.log('名称匹配结果:', nameMatch);
    if (nameMatch) {
        const extractedName = nameMatch[1].trim();
        console.log('提取的名称:', extractedName);
        // 自动填充脚本名称
        if (extractedName) {
            nameInput.value = extractedName;
            console.log('名称已填充到输入框');
        }
    }

    // 提取版本号 - 改进正则表达式
    const versionMatch = code.match(/@version\s+(.+?)(?:\r?\n|$)/i);
    console.log('版本号匹配结果:', versionMatch);
    if (versionMatch) {
        const extractedVersion = versionMatch[1].trim();
        console.log('提取的版本号:', extractedVersion);
        if (extractedVersion) {
            versionInput.value = extractedVersion;
            console.log('版本号已填充到输入框');
        }
    }

    // 【优化】不再自动清理头部信息，保留完整代码
    // 原来的代码会自动截取从 (function() { 开始的部分
    // 现在改为只提取信息，不修改代码内容

    // 检查是否包含 UserScript 头部信息
    const hasUserScriptHeader = code.includes('// ==UserScript==');

    // 显示处理结果提示
    const successMsg = document.createElement('div');
    successMsg.className = 'message success';

    if (hasUserScriptHeader) {
        successMsg.innerHTML = '<i class="fas fa-check-circle"></i> 脚本信息提取完成！已保留完整的 UserScript 头部信息';
    } else {
        successMsg.innerHTML = '<i class="fas fa-info-circle"></i> 脚本信息提取完成！未检测到 UserScript 头部，代码保持原样';
    }

    successMsg.style.marginTop = '10px';

    const form = document.getElementById('scriptForm');
    form.appendChild(successMsg);

    // 3秒后移除提示
    setTimeout(() => {
        if (successMsg.parentNode) {
            successMsg.parentNode.removeChild(successMsg);
        }
    }, 3000);
}

// 自动处理粘贴的内容
document.getElementById('scriptCode').addEventListener('paste', function(e) {
    setTimeout(() => {
        const code = this.value;
        if (code.includes('// ==UserScript==')) {
            // 延迟执行自动处理
            setTimeout(processScript, 100);
        }
    }, 50);
});

// 实时处理输入内容
document.getElementById('scriptCode').addEventListener('input', function(e) {
    const code = this.value;
    if (code.includes('// ==UserScript==')) {
        // 延迟执行自动处理，避免频繁触发
        clearTimeout(window.scriptInputTimeout);
        window.scriptInputTimeout = setTimeout(() => {
            processScript();
        }, 500); // 500ms延迟，避免输入时频繁触发
    }
});

// 页面加载完成后，检查是否有现有脚本内容需要处理
document.addEventListener('DOMContentLoaded', function() {
    const codeTextarea = document.getElementById('scriptCode');
    if (codeTextarea && codeTextarea.value.includes('// ==UserScript==')) {
        // 延迟处理，确保页面完全加载
        setTimeout(processScript, 200);
    }

    // 添加调试信息
    console.log('脚本管理页面已加载，自动识别功能已启用');
    console.log('脚本代码输入框:', document.getElementById('scriptCode'));
    console.log('脚本名称输入框:', document.getElementById('scriptName'));
    console.log('版本号输入框:', document.getElementById('scriptVersion'));
});

// 启用/禁用切换功能
function toggleStatus(scriptId) {
    const statusElement = event.target;
    const currentStatus = statusElement.textContent.trim();
    const newStatus = currentStatus === '启用' ? 'inactive' : 'active';
    
    // 禁用按钮防止重复点击
    statusElement.style.opacity = '0.6';
    statusElement.style.pointerEvents = 'none';
    
    // 发送AJAX请求到后端更新数据库
    const formData = new FormData();
    formData.append('action', 'toggle_script_status');
    formData.append('script_id', scriptId);
    formData.append('new_status', newStatus);
    
    fetch('../api/ajax_handler.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // 更新UI
            statusElement.textContent = newStatus === 'active' ? '启用' : '禁用';
            statusElement.className = 'status-badge ' + (newStatus === 'active' ? 'status-active' : 'status-disabled');
            showToast('脚本状态已更新', 'success');
        } else {
            showToast('状态更新失败: ' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('状态更新失败，请重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        statusElement.style.opacity = '1';
        statusElement.style.pointerEvents = 'auto';
    });
}

// 简单的提示函数
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    
    let backgroundColor, borderColor;
    switch(type) {
        case 'success':
            backgroundColor = 'rgba(34, 197, 94, 0.9)';
            borderColor = '#22c55e';
            break;
        case 'error':
            backgroundColor = 'rgba(239, 68, 68, 0.9)';
            borderColor = '#ef4444';
            break;
        case 'warning':
            backgroundColor = 'rgba(245, 158, 11, 0.9)';
            borderColor = '#f59e0b';
            break;
        default:
            backgroundColor = 'rgba(0, 0, 0, 0.8)';
            borderColor = 'rgba(255, 255, 255, 0.2)';
    }
    
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${backgroundColor};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        border: 1px solid ${borderColor};
        z-index: 10000;
        font-size: 14px;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        animation: slideInRight 0.3s ease;
    `;
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }, 3000);
}

// 添加动画CSS
if (!document.getElementById('toast-animations')) {
    const style = document.createElement('style');
    style.id = 'toast-animations';
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

// 权限选择功能
function updateWechatLabel() {
    const checkbox = document.getElementById('wechat_checkbox');
    const label = document.getElementById('wechat_label');

    if (!checkbox || !label) return;

    if (checkbox.checked) {
        label.style.background = 'rgba(138, 43, 226, 0.3)';
        label.style.borderColor = 'rgba(138, 43, 226, 0.6)';
        label.style.boxShadow = '0 0 15px rgba(138, 43, 226, 0.4)';
    } else {
        label.style.background = 'rgba(255, 255, 255, 0.1)';
        label.style.borderColor = 'rgba(255, 255, 255, 0.3)';
        label.style.boxShadow = 'none';
    }
}

function updateDouyinLabel() {
    const checkbox = document.getElementById('douyin_checkbox');
    const label = document.getElementById('douyin_label');

    if (!checkbox || !label) return;

    if (checkbox.checked) {
        label.style.background = 'rgba(138, 43, 226, 0.3)';
        label.style.borderColor = 'rgba(138, 43, 226, 0.6)';
        label.style.boxShadow = '0 0 15px rgba(138, 43, 226, 0.4)';
    } else {
        label.style.background = 'rgba(255, 255, 255, 0.1)';
        label.style.borderColor = 'rgba(255, 255, 255, 0.3)';
        label.style.boxShadow = 'none';
    }
}

function testPermissions() {
    const wechatChecked = document.getElementById('wechat_checkbox').checked;
    const douyinChecked = document.getElementById('douyin_checkbox').checked;

    let message = '当前权限状态:\n';
    message += '微信小店: ' + (wechatChecked ? '已选择' : '未选择') + '\n';
    message += '抖店: ' + (douyinChecked ? '已选择' : '未选择');

    alert(message);
}

// 页面加载时初始化权限状态
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保元素已渲染
    setTimeout(function() {
        updateWechatLabel();
        updateDouyinLabel();
    }, 100);
});


</script>