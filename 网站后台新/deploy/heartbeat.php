<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, X-API-Version, X-Request-Timestamp, X-Request-Signature');

require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once 'api_base.php';

// 创建API基础类实例
$api = new ApiBase();

// 接收请求
$request_body = file_get_contents('php://input');
$client_ip = get_client_ip();

try {
    // 解析请求数据
    $request_data = $api->parseRequest($request_body);
    
    if (empty($request_data)) {
        throw new Exception('无效的请求数据');
    }
    
    // 验证必要参数
    if (empty($request_data['key'])) {
        throw new Exception('卡密不能为空');
    }
    
    $key = $request_data['key'];
    $timestamp = $request_data['timestamp'] ?? time() * 1000;
    $client_info = $request_data['client_info'] ?? [];
    
    // 记录API调用
    $api->logApiCall($key, $client_ip, 'heartbeat', 'info', '收到心跳信号');
    
    // 检查卡密状态
    $license = $api->getLicenseByKey($key);
    
    // 如果找不到卡密或卡密状态异常
    if (!$license) {
        $api->logApiCall($key, $client_ip, 'heartbeat', 'error', '卡密不存在');
        throw new Exception('卡密不存在');
    }
    
    if ($license['status'] !== 'active') {
        $api->logApiCall($key, $client_ip, 'heartbeat', 'error', '卡密状态异常: ' . $license['status']);
        throw new Exception('卡密状态异常: ' . $license['status']);
    }
    
    // 更新最后心跳时间和IP地址
    $stmt = $pdo->prepare("
        UPDATE license_keys 
        SET last_heartbeat = NOW(), 
            last_used_ip = :ip,
            client_info = :client_info
        WHERE id = :id
    ");
    
    $client_info_json = !empty($client_info) ? json_encode($client_info) : null;
    
    $stmt->execute([
        ':ip' => $client_ip,
        ':client_info' => $client_info_json,
        ':id' => $license['id']
    ]);
    
    // 生成新的安全令牌
    $security_token = $api->generateSecurityToken($key, $license['id']);
    
    // 记录成功的心跳
    $api->logApiCall($key, $client_ip, 'heartbeat', 'success', '心跳更新成功');
    
    // 返回成功响应
    $response = [
        'success' => true,
        'message' => '心跳更新成功',
        'timestamp' => time() * 1000,
        'security_token' => $security_token
    ];
    
    echo $api->encryptResponse($response);
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
    $api->logApiCall(isset($key) ? $key : 'unknown', $client_ip, 'heartbeat', 'error', $error_message);
    
    $response = [
        'success' => false,
        'message' => $error_message,
        'timestamp' => time() * 1000
    ];
    
    echo json_encode($response);
} 