<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 尝试加载安全监控，如果失败则使用简化版本
try {
    if (file_exists('../includes/security_monitor.php')) {
        require_once '../includes/security_monitor.php';
    }
} catch (Exception $e) {
    error_log("安全监控加载失败: " . $e->getMessage());
}

// 如果安全监控函数不存在，创建简化版本
if (!function_exists('getSecurityMonitor')) {
    function getSecurityMonitor() {
        return new class {
            public function checkPageSecurity($user_id, $page) {
                // 简化的安全检查，总是返回true
                return true;
            }
        };
    }
}

// 检查登录状态
require_login();

$current_page = $_GET['page'] ?? 'dashboard';

// 安全检查：检查页面访问权限
try {
    $security_monitor = getSecurityMonitor();
    $page_security_check = $security_monitor->checkPageSecurity($_SESSION['admin_user_id'], $current_page);
} catch (Exception $e) {
    // 如果安全检查失败，记录错误但允许访问
    error_log("安全检查失败: " . $e->getMessage());
    $page_security_check = true;
}

// 如果是陌生设备访问敏感页面，显示安全警告页面
if (!$page_security_check) {
    if (file_exists('templates/security_warning.php')) {
        include 'templates/security_warning.php';
    } else {
        echo "<div style='padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin: 20px;'>";
        echo "<h3>安全提醒</h3>";
        echo "<p>检测到您使用的是新设备，为了账户安全，请联系管理员验证身份。</p>";
        echo "<p><a href='login.php'>返回登录页面</a></p>";
        echo "</div>";
    }
    exit;
}

// 获取用户信息
$stmt = $pdo->prepare("SELECT * FROM admin_users WHERE id = ?");
$stmt->execute([$_SESSION['admin_user_id']]);
$admin_user = $stmt->fetch();

if (!$admin_user) {
    logout();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI客服系统 - 管理后台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/style.css?v=<?php echo time(); ?>">
    <!-- 强制刷新CSS缓存，确保最新样式生效 -->
    <script src="../assets/modal.js?v=<?php echo time(); ?>"></script>
    <!-- 自定义弹窗组件 -->
</head>
<body>
    <!-- 动态背景粒子 -->
    <div class="particles" id="particles"></div>
    
    <!-- 主内容包装器 -->
    <div class="main-wrapper">
        <!-- 主头部 -->
        <header class="main-header">
            <div class="header-left">
                <h1 class="header-title">
                    <i class="fas fa-robot"></i>
                    小梅花AI客服系统
                </h1>
            </div>
            <div class="header-right">
                <div class="header-user">
                    <div class="user-info">
                        <span class="user-name"><?php echo htmlspecialchars($admin_user['username']); ?></span>
                        <span class="user-role">系统管理员</span>
                    </div>
                    <i class="fas fa-user-circle user-avatar"></i>
                    
                    <div class="user-dropdown">
                        <a href="index.php?page=settings" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            <span>系统设置</span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="logout.php" class="dropdown-item logout-item" onclick="return confirm('确定要退出登录吗？')">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>退出登录</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <?php
            switch ($current_page) {
                case 'dashboard':
                    include 'templates/dashboard.php';
                    break;
                case 'homepage':
                    include 'templates/homepage.php';
                    break;
                case 'keys':
                    include 'templates/keys.php';
                    break;
                case 'scripts':
                    include 'templates/scripts.php';
                    break;
                case 'users':
                    include 'templates/users.php';
                    break;
                case 'analytics':
                    include 'templates/analytics.php';
                    break;

                case 'settings':
                    include 'templates/settings.php';
                    break;
                default:
                    include 'templates/dashboard.php';
                    break;
            }
            ?>
        </main>
    </div>
    
    <!-- 侧边栏 -->
    <aside class="sidebar" id="sidebar">
        
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="?page=dashboard" class="nav-link <?php echo $current_page === 'dashboard' ? 'active' : ''; ?>" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span class="nav-text">总数据</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=keys" class="nav-link <?php echo $current_page === 'keys' ? 'active' : ''; ?>" data-page="keys">
                        <i class="fas fa-key"></i>
                        <span class="nav-text">卡密管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=scripts" class="nav-link <?php echo $current_page === 'scripts' ? 'active' : ''; ?>" data-page="scripts">
                        <i class="fas fa-code"></i>
                        <span class="nav-text">脚本管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=users" class="nav-link <?php echo $current_page === 'users' ? 'active' : ''; ?>" data-page="users">
                        <i class="fas fa-users"></i>
                        <span class="nav-text">用户管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=analytics" class="nav-link <?php echo $current_page === 'analytics' ? 'active' : ''; ?>" data-page="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span class="nav-text">数据分析</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a href="?page=settings" class="nav-link <?php echo $current_page === 'settings' ? 'active' : ''; ?>" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span class="nav-text">系统设置</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <div class="version-info">
                <small>测试版本 v1.0</small>
            </div>
        </div>
    </aside>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 防止表单重新提交弹窗的全局解决方案
        (function() {
            'use strict';

            // 防止页面刷新时出现表单重新提交的提示
            function preventFormResubmissionDialog() {
                // 使用 history.replaceState 清除 POST 历史记录
                if (window.history.replaceState && window.location.search === '') {
                    window.history.replaceState(null, null, window.location.pathname);
                }

                // 如果URL中有查询参数，也清除POST状态
                if (window.history.replaceState) {
                    const url = new URL(window.location);
                    // 保留查询参数，但清除POST状态
                    window.history.replaceState(null, null, url.toString());
                }
            }

            // 表单提交后的处理
            function handleFormSubmission() {
                const forms = document.querySelectorAll('form');

                forms.forEach(form => {
                    // 跳过GET方法的表单
                    if (form.method.toLowerCase() === 'get') {
                        return;
                    }

                    form.addEventListener('submit', function(e) {
                        // 标记表单已提交
                        sessionStorage.setItem('form_submitted_' + Date.now(), 'true');

                        // 延迟执行，确保表单提交完成后再清除历史记录
                        setTimeout(() => {
                            preventFormResubmissionDialog();
                        }, 100);
                    });
                });
            }

            // 页面加载时执行
            function initializeFormProtection() {
                // 立即清除可能的POST状态
                preventFormResubmissionDialog();

                // 设置表单提交监听器
                handleFormSubmission();

                // 清理旧的session标记
                Object.keys(sessionStorage).forEach(key => {
                    if (key.startsWith('form_submitted_')) {
                        const timestamp = parseInt(key.split('_')[2]);
                        // 清理5分钟前的标记
                        if (Date.now() - timestamp > 300000) {
                            sessionStorage.removeItem(key);
                        }
                    }
                });
            }

            // 页面可见性变化时也执行清理
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    preventFormResubmissionDialog();
                }
            });

            // 浏览器前进后退时执行清理
            window.addEventListener('popstate', function() {
                preventFormResubmissionDialog();
            });

            // DOM加载完成后初始化
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeFormProtection);
            } else {
                initializeFormProtection();
            }

            // 页面完全加载后再次执行
            window.addEventListener('load', function() {
                setTimeout(preventFormResubmissionDialog, 500);
            });
        })();

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始初始化...');
            initializeNavigation();
            initializeSubmenu();
            initializeParticles();
            initializeResponsive();
        });

        // 初始化导航
        function initializeNavigation() {
            console.log('📋 初始化导航...');
            const navLinks = document.querySelectorAll('.nav-link, .submenu-link');
            const currentPage = new URLSearchParams(window.location.search).get('page') || 'dashboard';
            console.log('当前页面:', currentPage);
            
            navLinks.forEach(link => {
                const page = link.getAttribute('data-page');
                if (page === currentPage) {
                    link.classList.add('active');
                    console.log('激活导航项:', page);
                } else {
                    link.classList.remove('active');
                }
            });
        }

        // 初始化二级菜单功能
        function initializeSubmenu() {
            console.log('🔧 初始化二级菜单...');
            const hasSubmenuItems = document.querySelectorAll('.nav-item.has-submenu');
            console.log('找到二级菜单项数量:', hasSubmenuItems.length);
            
            hasSubmenuItems.forEach((item, index) => {
                const navLink = item.querySelector('.nav-link');
                const submenu = item.querySelector('.submenu');
                const submenuLinks = item.querySelectorAll('.submenu-link');
                
                console.log(`设置菜单项 ${index + 1}:`, navLink ? navLink.textContent.trim() : 'null');
                
                if (navLink && submenu) {
                    // 一级菜单点击事件
                    navLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('🔄 切换二级菜单状态');
                        
                        // 切换当前菜单的展开状态
                        const isActive = item.classList.contains('active');
                        
                        if (isActive) {
                            item.classList.remove('active');
                            console.log('📂 收起菜单');
                        } else {
                            item.classList.add('active');
                            console.log('📁 展开菜单');
                        }
                        
                        // 关闭其他展开的子菜单
                        hasSubmenuItems.forEach(otherItem => {
                            if (otherItem !== item) {
                                otherItem.classList.remove('active');
                            }
                        });
                    });
                    
                    // 二级菜单项点击事件 - 不关闭父菜单
                    submenuLinks.forEach(submenuLink => {
                        submenuLink.addEventListener('click', function(e) {
                            // 不阻止默认行为，允许页面跳转
                            console.log('🎯 点击二级菜单项，保持父菜单展开状态');
                            
                            // 移除其他二级菜单项的激活状态
                            submenuLinks.forEach(link => link.classList.remove('active'));
                            
                            // 激活当前二级菜单项
                            this.classList.add('active');
                            
                            // 确保父菜单保持展开状态
                            item.classList.add('active');
                        });
                    });
                } else {
                    console.warn('⚠️ 菜单项缺少必要元素:', {navLink, submenu});
                }
            });
            
            // 检查当前页面，如果是二级菜单页面，自动展开对应的父菜单
            const currentPage = new URLSearchParams(window.location.search).get('page') || 'dashboard';
            
            console.log('✅ 二级菜单初始化完成');
        }

        // 初始化响应式功能
        function initializeResponsive() {
            // 监听窗口大小变化
            window.addEventListener('resize', handleResize);
            handleResize(); // 初始化时执行一次
        }

        // 处理窗口大小变化
        function handleResize() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');
            
            if (window.innerWidth > 768) {
                // 桌面端：显示侧边栏，隐藏遮罩
                if (sidebar) sidebar.classList.remove('mobile-open');
                if (overlay) overlay.classList.remove('active');
            } else {
                // 移动端：确保侧边栏默认隐藏
                if (sidebar) sidebar.classList.remove('mobile-open');
                if (overlay) overlay.classList.remove('active');
            }
        }

        // 初始化粒子背景
        function initializeParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;
            
            // 清空现有粒子
            particlesContainer.innerHTML = '';
            
            // 根据设备调整粒子数量
            const particleCount = window.innerWidth < 768 ? 30 : 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // 随机位置和大小
                const size = Math.random() * 4 + 2;
                const x = Math.random() * 100;
                const y = Math.random() * 100;
                const duration = Math.random() * 20 + 10;
                const delay = Math.random() * 5;
                
                particle.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 50%;
                    left: ${x}%;
                    top: ${y}%;
                    animation: float ${duration}s ${delay}s infinite ease-in-out;
                    pointer-events: none;
                `;
                
                particlesContainer.appendChild(particle);
            }
        }

        // 头部用户下拉菜单功能
        document.addEventListener('click', function(event) {
            const headerUser = document.querySelector('.header-user');
            const userDropdown = document.querySelector('.user-dropdown');
            
            if (headerUser && userDropdown) {
                if (headerUser.contains(event.target)) {
                    // 如果点击的是下拉菜单内的链接，不切换状态
                    if (!event.target.closest('.dropdown-item')) {
                        userDropdown.classList.toggle('active');
                    }
                } else {
                    userDropdown.classList.remove('active');
                }
            }
        });
        
        // 添加浮动动画CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                0%, 100% { transform: translateY(0px); opacity: 0.3; }
                50% { transform: translateY(-20px); opacity: 0.7; }
            }
            
            .particles {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 1;
                overflow: hidden;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html> 