<?php
/**
 * 安全API配置管理系统
 * 版本: v2.0.0
 * 功能: 管理API密钥、JWT令牌、数据库连接等敏感信息
 */

// 防止直接访问
if (!defined('API_SECURITY_INIT')) {
    http_response_code(403);
    exit('Access denied');
}

class SecureApiConfig {
    private static $instance = null;
    private $config = [];
    private $encryptionKey;
    
    private function __construct() {
        $this->encryptionKey = $this->getEncryptionKey();
        $this->loadConfig();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 获取加密密钥 - 从环境变量或KMS获取
     */
    private function getEncryptionKey() {
        // 优先从环境变量获取
        $key = getenv('XIAOMEIHUA_ENCRYPTION_KEY');
        if ($key) {
            return $key;
        }
        
        // 从配置文件获取（加密存储）
        $keyFile = __DIR__ . '/../config/.encryption_key';
        if (file_exists($keyFile)) {
            return trim(file_get_contents($keyFile));
        }
        
        // 生成新密钥并保存
        $newKey = bin2hex(random_bytes(32));
        $this->saveEncryptionKey($newKey);
        return $newKey;
    }
    
    /**
     * 保存加密密钥到安全位置
     */
    private function saveEncryptionKey($key) {
        $configDir = __DIR__ . '/../config';
        if (!is_dir($configDir)) {
            mkdir($configDir, 0750, true);
        }
        
        $keyFile = $configDir . '/.encryption_key';
        file_put_contents($keyFile, $key, LOCK_EX);
        chmod($keyFile, 0600); // 只有所有者可读写
    }
    
    /**
     * 加载配置信息
     */
    private function loadConfig() {
        // JWT配置
        $this->config['jwt'] = [
            'secret' => $this->generateJwtSecret(),
            'algorithm' => 'HS256',
            'access_token_expire' => 900, // 15分钟
            'refresh_token_expire' => 604800, // 7天
            'issuer' => 'xiaomeihua-api',
            'audience' => 'xiaomeihua-client'
        ];
        
        // API配置
        $this->config['api'] = [
            'rate_limit' => 100, // 每分钟请求限制
            'allowed_origins' => $this->getAllowedOrigins(),
            'api_version' => 'v2.0',
            'encryption_enabled' => true
        ];
        
        // 数据库配置（从环境变量获取）
        $this->config['database'] = [
            'host' => $this->getSecureConfig('DB_HOST', 'localhost'),
            'port' => $this->getSecureConfig('DB_PORT', '3306'),
            'name' => $this->getSecureConfig('DB_NAME', ''),
            'user' => $this->getSecureConfig('DB_USER', ''),
            'pass' => $this->getSecureConfig('DB_PASS', ''),
            'charset' => 'utf8mb4',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]
        ];
        
        // 安全配置
        $this->config['security'] = [
            'password_salt' => $this->generateSalt(),
            'session_timeout' => 3600, // 1小时
            'max_login_attempts' => 5,
            'lockout_duration' => 1800, // 30分钟
            'require_2fa' => false,
            'ip_whitelist_enabled' => false,
            'ip_whitelist' => []
        ];
    }
    
    /**
     * 生成JWT密钥
     */
    private function generateJwtSecret() {
        $secret = getenv('JWT_SECRET');
        if ($secret) {
            return $secret;
        }
        
        // 使用加密密钥生成JWT密钥
        return hash_hmac('sha256', 'jwt_secret', $this->encryptionKey);
    }
    
    /**
     * 生成密码盐值
     */
    private function generateSalt() {
        return hash_hmac('sha256', 'password_salt', $this->encryptionKey);
    }
    
    /**
     * 获取允许的来源域名
     */
    private function getAllowedOrigins() {
        $origins = getenv('ALLOWED_ORIGINS');
        if ($origins) {
            return explode(',', $origins);
        }
        
        // 默认允许的域名
        return [
            'https://xiaomeihuakefu.cn',
            'https://www.xiaomeihuakefu.cn',
            'https://store.weixin.qq.com'
        ];
    }
    
    /**
     * 安全获取配置项
     */
    private function getSecureConfig($key, $default = null) {
        // 优先从环境变量获取
        $value = getenv($key);
        if ($value !== false) {
            return $value;
        }
        
        // 从加密配置文件获取
        $configFile = __DIR__ . '/../config/.env.encrypted';
        if (file_exists($configFile)) {
            $encryptedData = file_get_contents($configFile);
            $decryptedData = $this->decrypt($encryptedData);
            $envData = parse_ini_string($decryptedData);
            
            if (isset($envData[$key])) {
                return $envData[$key];
            }
        }
        
        return $default;
    }
    
    /**
     * 获取配置项
     */
    public function get($section, $key = null) {
        if ($key === null) {
            return $this->config[$section] ?? null;
        }
        
        return $this->config[$section][$key] ?? null;
    }
    
    /**
     * 设置配置项
     */
    public function set($section, $key, $value) {
        if (!isset($this->config[$section])) {
            $this->config[$section] = [];
        }
        
        $this->config[$section][$key] = $value;
    }
    
    /**
     * 加密数据
     */
    public function encrypt($data) {
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $this->encryptionKey, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * 解密数据
     */
    public function decrypt($encryptedData) {
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', $this->encryptionKey, 0, $iv);
    }
    
    /**
     * 保存加密配置到文件
     */
    public function saveEncryptedConfig($configData) {
        $configDir = __DIR__ . '/../config';
        if (!is_dir($configDir)) {
            mkdir($configDir, 0750, true);
        }
        
        $envString = '';
        foreach ($configData as $key => $value) {
            $envString .= $key . '=' . $value . "\n";
        }
        
        $encrypted = $this->encrypt($envString);
        $configFile = $configDir . '/.env.encrypted';
        file_put_contents($configFile, $encrypted, LOCK_EX);
        chmod($configFile, 0600);
    }
    
    /**
     * 获取数据库连接
     */
    public function getDatabaseConnection() {
        // 使用MySQL数据库连接（宝塔服务器）
        $dbConfig = $this->config['database'];
        
        try {
            $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['name']};charset={$dbConfig['charset']}";
            $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], $dbConfig['options']);
            return $pdo;
        } catch (PDOException $e) {
            error_log("MySQL数据库连接失败: " . $e->getMessage());
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 验证API密钥
     */
    public function validateApiKey($apiKey) {
        // 实现API密钥验证逻辑
        $validKeys = $this->getValidApiKeys();
        return in_array($apiKey, $validKeys);
    }
    
    /**
     * 获取有效的API密钥列表
     */
    private function getValidApiKeys() {
        // 从数据库或配置文件获取有效的API密钥
        return [
            hash_hmac('sha256', 'api_key_1', $this->encryptionKey),
            hash_hmac('sha256', 'api_key_2', $this->encryptionKey)
        ];
    }
}

// 初始化API安全配置
if (!defined('API_SECURITY_INIT')) {
    define('API_SECURITY_INIT', true);
}
$apiConfig = SecureApiConfig::getInstance(); 