<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, X-API-Version, X-Request-Timestamp, X-Request-Signature');

require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once 'api_base.php';

// 创建API基础类实例
$api = new ApiBase();

// 接收请求
$request_body = file_get_contents('php://input');
$client_ip = get_client_ip();

try {
    // 解析请求数据
    $request_data = $api->parseRequest($request_body);
    
    if (empty($request_data)) {
        throw new Exception('无效的请求数据');
    }
    
    // 验证必要参数
    if (empty($request_data['key'])) {
        throw new Exception('卡密不能为空');
    }
    
    $key = $request_data['key'];
    $action = $request_data['action'] ?? 'check';
    $security_token = $request_data['security_token'] ?? '';
    
    // 记录API调用
    $api->logApiCall($key, $client_ip, 'status', 'info', "收到状态请求: $action");
    
    // 处理注销请求
    if ($action === 'logout') {
        // 验证安全令牌
        if (!$api->validateSecurityToken($key, $security_token)) {
            throw new Exception('安全令牌无效，请重新登录');
        }
        
        // 记录注销操作
        $api->logApiCall($key, $client_ip, 'status', 'info', '用户主动注销');
        
        // 返回成功响应
        $response = [
            'success' => true,
            'message' => '注销成功',
            'timestamp' => time() * 1000
        ];
        
        echo $api->encryptResponse($response);
        exit;
    }
    
    // 检查卡密状态
    $license = $api->getLicenseByKey($key);
    
    // 如果找不到卡密
    if (!$license) {
        $api->logApiCall($key, $client_ip, 'status', 'error', '卡密不存在');
        
        $response = [
            'success' => false,
            'message' => '卡密不存在',
            'error_code' => 'KEY_DELETED',
            'timestamp' => time() * 1000
        ];
        
        echo $api->encryptResponse($response);
        exit;
    }
    
    // 检查卡密状态
    if ($license['status'] !== 'active') {
        $error_code = '';
        $message = '';
        
        switch ($license['status']) {
            case 'disabled':
                $error_code = 'KEY_DISABLED';
                $message = '您的卡密已被禁用，如有疑问请联系代理商';
                break;
            case 'expired':
                $error_code = 'KEY_EXPIRED';
                $message = '您的卡密已过期，如需继续使用请联系代理商';
                break;
            case 'suspended':
                $error_code = 'KEY_DISABLED';
                $message = '您的卡密已被暂停使用，如有疑问请联系代理商';
                break;
            default:
                $error_code = 'KEY_DISABLED';
                $message = '您的卡密状态异常，如有疑问请联系代理商';
                break;
        }
        
        $api->logApiCall($key, $client_ip, 'status', 'error', "卡密状态异常: {$license['status']}");
        
        $response = [
            'success' => false,
            'message' => $message,
            'error_code' => $error_code,
            'status' => $license['status'],
            'timestamp' => time() * 1000
        ];
        
        echo $api->encryptResponse($response);
        exit;
    }
    
    // 检查过期时间
    $now = new DateTime();
    $expiry_date = new DateTime($license['expiry_date']);
    
    if ($now > $expiry_date) {
        // 自动更新过期卡密状态
        $stmt = $pdo->prepare("UPDATE license_keys SET status = 'expired' WHERE id = :id");
        $stmt->execute([':id' => $license['id']]);
        
        $api->logApiCall($key, $client_ip, 'status', 'error', '卡密已过期');
        
        $response = [
            'success' => false,
            'message' => '您的卡密已过期，如需继续使用请联系代理商',
            'error_code' => 'KEY_EXPIRED',
            'timestamp' => time() * 1000
        ];
        
        echo $api->encryptResponse($response);
        exit;
    }
    
    // 生成新的安全令牌
    $security_token = $api->generateSecurityToken($key, $license['id']);
    
    // 更新最后检查时间
    $stmt = $pdo->prepare("
        UPDATE license_keys 
        SET last_check = NOW(), 
            last_used_ip = :ip
        WHERE id = :id
    ");
    
    $stmt->execute([
        ':ip' => $client_ip,
        ':id' => $license['id']
    ]);
    
    // 确定功能类型
    $has_customer_service = $license['has_customer_service'] ?? 1;
    $has_product_listing = $license['has_product_listing'] ?? 0;
    
    $function_type = '';
    if ($has_customer_service && $has_product_listing) {
        $function_type = 'full_features';
    } elseif ($has_product_listing && !$has_customer_service) {
        $function_type = 'product_listing';
    } elseif ($has_customer_service && !$has_product_listing) {
        $function_type = 'customer_service';
    } else {
        $function_type = 'no_features';
    }
    
    // 记录成功的状态检查
    $api->logApiCall($key, $client_ip, 'status', 'success', '状态检查成功');
    
    // 返回成功响应
    $response = [
        'success' => true,
        'message' => '卡密状态正常',
        'function_type' => $function_type,
        'has_customer_service' => (bool)$has_customer_service,
        'has_product_listing' => (bool)$has_product_listing,
        'expiry_date' => $license['expiry_date'],
        'status' => $license['status'],
        'timestamp' => time() * 1000,
        'security_token' => $security_token
    ];
    
    echo $api->encryptResponse($response);
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
    $api->logApiCall(isset($key) ? $key : 'unknown', $client_ip, 'status', 'error', $error_message);
    
    $response = [
        'success' => false,
        'message' => $error_message,
        'timestamp' => time() * 1000
    ];
    
    echo json_encode($response);
} 