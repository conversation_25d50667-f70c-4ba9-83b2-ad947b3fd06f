<?php
/**
 * APP设置API接口 - 服务器环境版本
 * 处理APP弹窗、协议、更新等功能的API请求
 * 适用于服务器部署环境
 */

// 定义API访问常量
define('API_ACCESS', true);

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入必要的文件
require_once __DIR__ . '/../includes/functions.php';

// 数据库连接
try {
    require_once __DIR__ . '/../includes/db.php';
    $database_available = is_database_available();
} catch (Exception $e) {
    $database_available = false;
    error_log('数据库连接失败: ' . $e->getMessage());
}

/**
 * APP设置API处理类
 */
class AppSettingsAPI {
    private $db;
    private $method;
    private $endpoint;
    private $params;
    
    public function __construct() {
        global $pdo, $database_available;

        $this->db = $pdo;
        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->parseUrl();

        // 如果数据库可用，初始化表
        if ($database_available && $this->db) {
            $this->initializeTables();
        }
    }
    
    /**
     * 解析URL获取endpoint和参数
     */
    private function parseUrl() {
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        // 移除多种可能的路径前缀
        $patterns = [
            '/api/app_settings.php/',
            '/api/app_settings/',
            '/app_settings.php/',
            '/app_settings/'
        ];
        
        foreach ($patterns as $pattern) {
            if (strpos($path, $pattern) !== false) {
                $path = str_replace($pattern, '', $path);
                break;
            }
        }
        
        // 如果路径仍然包含文件名，移除它
        $path = str_replace('app_settings.php', '', $path);
        $path = trim($path, '/');
        
        $pathParts = $path ? explode('/', $path) : [];
        
        $this->endpoint = $pathParts[0] ?? '';
        $this->params = array_slice($pathParts, 1);
    }
    
    /**
     * 初始化数据库表
     */
    private function initializeTables() {
        try {
            // 检测数据库类型
            $driver = $this->db->getAttribute(PDO::ATTR_DRIVER_NAME);
            $is_sqlite = ($driver === 'sqlite');

            // 创建弹窗表
            if ($is_sqlite) {
                $sql = "CREATE TABLE IF NOT EXISTS app_popups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    type TEXT NOT NULL DEFAULT 'once',
                    custom_days INTEGER DEFAULT NULL,
                    status TEXT DEFAULT 'active',
                    priority INTEGER DEFAULT 1,
                    target_audience TEXT DEFAULT 'all',
                    display_count INTEGER DEFAULT 0,
                    click_count INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )";
            } else {
                $sql = "CREATE TABLE IF NOT EXISTS app_popups (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    content TEXT NOT NULL,
                    type VARCHAR(50) DEFAULT 'once',
                    custom_days INT DEFAULT NULL,
                    status VARCHAR(20) DEFAULT 'active',
                    priority INT DEFAULT 1,
                    target_audience VARCHAR(50) DEFAULT 'all',
                    display_count INT DEFAULT 0,
                    click_count INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            }
            $this->db->exec($sql);

            // 检查并更新现有表结构
            try {
                // 检查type字段是否为ENUM类型，如果是则需要更新
                $stmt = $this->db->query("SHOW COLUMNS FROM app_popups LIKE 'type'");
                $column = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($column && strpos($column['Type'], 'enum') !== false) {
                    // 更新type字段为JSON类型
                    $this->db->exec("ALTER TABLE app_popups MODIFY COLUMN type JSON NOT NULL");

                    // 添加custom_days字段（如果不存在）
                    $stmt = $this->db->query("SHOW COLUMNS FROM app_popups LIKE 'custom_days'");
                    if (!$stmt->fetch()) {
                        $this->db->exec("ALTER TABLE app_popups ADD COLUMN custom_days INT DEFAULT NULL AFTER type");
                    }

                    // 更新现有数据
                    $this->db->exec("UPDATE app_popups SET type = JSON_ARRAY(type) WHERE JSON_VALID(type) = 0");
                }
            } catch (Exception $e) {
                error_log("更新弹窗表结构失败: " . $e->getMessage());
            }
            
            // 创建协议表
            $sql = "CREATE TABLE IF NOT EXISTS app_agreements (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content LONGTEXT NOT NULL,
                type VARCHAR(50) DEFAULT 'privacy' COMMENT '协议类型: privacy, terms, etc',
                version VARCHAR(20) DEFAULT '1.0' COMMENT '协议版本',
                status ENUM('draft', 'published') DEFAULT 'draft' COMMENT '协议状态',
                sort_order INT DEFAULT 0 COMMENT '排序顺序，数字越小越靠前',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_type (type),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at),
                INDEX idx_sort_order (sort_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $this->db->exec($sql);

            // 检查并添加新字段（为了兼容已存在的表）
            try {
                // 检查type字段是否存在
                $result = $this->db->query("SHOW COLUMNS FROM app_agreements LIKE 'type'");
                if ($result->rowCount() == 0) {
                    $this->db->exec("ALTER TABLE app_agreements ADD COLUMN type VARCHAR(50) DEFAULT 'privacy' COMMENT '协议类型' AFTER content");
                }

                // 检查version字段是否存在
                $result = $this->db->query("SHOW COLUMNS FROM app_agreements LIKE 'version'");
                if ($result->rowCount() == 0) {
                    $this->db->exec("ALTER TABLE app_agreements ADD COLUMN version VARCHAR(20) DEFAULT '1.0' COMMENT '协议版本' AFTER type");
                }

                // 添加索引（如果不存在）
                $this->db->exec("CREATE INDEX IF NOT EXISTS idx_type ON app_agreements (type)");
                $this->db->exec("CREATE INDEX IF NOT EXISTS idx_status ON app_agreements (status)");
                $this->db->exec("CREATE INDEX IF NOT EXISTS idx_created_at ON app_agreements (created_at)");
            } catch (Exception $e) {
                error_log("更新协议表结构失败: " . $e->getMessage());
            }
            
            // 创建APP更新表
            $sql = "CREATE TABLE IF NOT EXISTS app_updates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                version VARCHAR(50) NULL DEFAULT '1.0.0',
                title VARCHAR(255) NULL DEFAULT '新版本更新',
                description TEXT NULL DEFAULT '本次更新包含性能优化和bug修复',
                exe_file VARCHAR(255) NULL,
                dmg_file VARCHAR(255) NULL,
                force_update BOOLEAN DEFAULT TRUE,
                status ENUM('draft', 'published') DEFAULT 'draft',
                download_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $this->db->exec($sql);

            // 更新现有表结构以支持新的默认值
            try {
                // 修改现有字段为可选并设置默认值
                $this->db->exec("ALTER TABLE app_updates
                    MODIFY COLUMN version VARCHAR(50) NULL DEFAULT '1.0.0',
                    MODIFY COLUMN title VARCHAR(255) NULL DEFAULT '新版本更新',
                    MODIFY COLUMN description TEXT NULL DEFAULT '本次更新包含性能优化和bug修复',
                    MODIFY COLUMN force_update BOOLEAN DEFAULT TRUE");
            } catch (Exception $e) {
                error_log("更新app_updates表结构失败: " . $e->getMessage());
            }
        } catch (Exception $e) {
            error_log("初始化数据库表失败: " . $e->getMessage());
        }
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        try {
            // 检查数据库连接
            if (!$this->db) {
                return $this->sendError('Database connection failed', 500);
            }
            
            switch ($this->endpoint) {
                case 'popup':
                    return $this->handlePopup();
                case 'agreement':
                    return $this->handleAgreement();
                case 'update':
                    return $this->handleUpdate();

                case 'test':
                    return $this->handleTest();
                default:
                    return $this->sendError('Invalid endpoint', 404);
            }
        } catch (Exception $e) {
            error_log('APP Settings API Error: ' . $e->getMessage());
            return $this->sendError('Internal server error', 500);
        }
    }
    
    /**
     * 处理测试请求
     */
    private function handleTest() {
        global $database_available;
        
        $testData = [
            'api_status' => 'working',
            'database_status' => $database_available ? 'connected' : 'disconnected',
            'database_type' => 'MySQL',
            'server_time' => date('Y-m-d H:i:s'),
            'endpoints' => ['popup', 'agreement', 'update']
        ];
        
        return $this->sendSuccess($testData, 'API test successful');
    }
    
    /**
     * 处理弹窗相关请求
     */
    private function handlePopup() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getPopupList();
                    } elseif ($this->params[0] === 'active') {
                        return $this->getActivePopup();
                    } else {
                        return $this->getPopup($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request', 400);
                
            case 'POST':
                if (isset($this->params[0]) && $this->params[0] === 'log') {
                    return $this->logPopupAction();
                }
                return $this->createPopup();
                
            case 'PUT':
                if (isset($this->params[0])) {
                    return $this->updatePopup($this->params[0]);
                }
                return $this->sendError('ID required', 400);
                
            case 'DELETE':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'all') {
                        return $this->deleteAllPopups();
                    } else {
                        return $this->deletePopup($this->params[0]);
                    }
                }
                return $this->sendError('ID required', 400);
                
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 获取弹窗列表
     */
    private function getPopupList() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->query("SELECT * FROM app_popups ORDER BY priority DESC, created_at DESC");
            $popups = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 处理数据格式
            foreach ($popups as &$popup) {
                // 确保type字段格式正确
                if (isset($popup['type'])) {
                    // 如果是JSON格式，解析它
                    if (is_string($popup['type']) && (strpos($popup['type'], '[') === 0 || strpos($popup['type'], '{') === 0)) {
                        $popup['type'] = json_decode($popup['type'], true);
                        if (!is_array($popup['type'])) {
                            $popup['type'] = [$popup['type']];
                        }
                    } else {
                        // 如果是简单字符串，转换为数组
                        $popup['type'] = is_array($popup['type']) ? $popup['type'] : [$popup['type']];
                    }
                }

                // 确保数值字段为整数
                $popup['id'] = (int)$popup['id'];
                $popup['priority'] = (int)($popup['priority'] ?? 1);
                $popup['display_count'] = (int)($popup['display_count'] ?? 0);
                $popup['click_count'] = (int)($popup['click_count'] ?? 0);
                $popup['custom_days'] = $popup['custom_days'] ? (int)$popup['custom_days'] : null;
            }

            return $this->sendSuccess(['popups' => $popups]);
        } catch (Exception $e) {
            error_log('获取弹窗列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get popup list: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取单个弹窗
     */
    private function getPopup($id) {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->prepare("SELECT * FROM app_popups WHERE id = ?");
            $stmt->execute([$id]);
            $popup = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$popup) {
                return $this->sendError('Popup not found', 404);
            }

            // 处理数据格式
            if (isset($popup['type'])) {
                if (is_string($popup['type']) && (strpos($popup['type'], '[') === 0 || strpos($popup['type'], '{') === 0)) {
                    $popup['type'] = json_decode($popup['type'], true);
                    if (!is_array($popup['type'])) {
                        $popup['type'] = [$popup['type']];
                    }
                } else {
                    $popup['type'] = is_array($popup['type']) ? $popup['type'] : [$popup['type']];
                }
            }

            // 确保数值字段为整数
            $popup['id'] = (int)$popup['id'];
            $popup['priority'] = (int)($popup['priority'] ?? 1);
            $popup['display_count'] = (int)($popup['display_count'] ?? 0);
            $popup['click_count'] = (int)($popup['click_count'] ?? 0);
            $popup['custom_days'] = $popup['custom_days'] ? (int)$popup['custom_days'] : null;

            return $this->sendSuccess(['popup' => $popup]);
        } catch (Exception $e) {
            error_log('获取弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to get popup: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 创建弹窗
     */
    private function createPopup() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $data = $this->getRequestData();

            // 验证必填字段
            if (empty($data['title']) || empty($data['content'])) {
                return $this->sendError('Missing required fields: title and content', 400);
            }

            // 处理type字段
            $type = 'once'; // 默认值
            if (isset($data['type'])) {
                if (is_array($data['type'])) {
                    $type = implode(',', $data['type']);
                } else {
                    $type = $data['type'];
                }
            }

            // 验证类型
            $validTypes = ['once', 'daily_7', 'weekly', 'always', 'custom'];
            $typeArray = explode(',', $type);
            foreach ($typeArray as $t) {
                $t = trim($t);
                if (!in_array($t, $validTypes)) {
                    return $this->sendError('Invalid type: ' . $t, 400);
                }
            }

            // 处理其他字段
            $customDays = null;
            if (in_array('custom', $typeArray) && isset($data['custom_days'])) {
                $customDays = intval($data['custom_days']);
                if ($customDays < 1 || $customDays > 365) {
                    return $this->sendError('Invalid custom days (1-365)', 400);
                }
            }

            $priority = isset($data['priority']) ? intval($data['priority']) : 1;
            $status = isset($data['status']) ? $data['status'] : 'active';
            $targetAudience = isset($data['target_audience']) ? $data['target_audience'] : 'all';

            // 验证状态
            if (!in_array($status, ['active', 'inactive'])) {
                $status = 'active';
            }

            // 验证目标受众
            if (!in_array($targetAudience, ['all', 'new_users', 'existing_users'])) {
                $targetAudience = 'all';
            }

            // 插入数据
            $sql = "INSERT INTO app_popups (title, content, type, custom_days, status, priority, target_audience) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $this->db->prepare($sql);
            $success = $stmt->execute([
                $data['title'],
                $data['content'],
                $type,
                $customDays,
                $status,
                $priority,
                $targetAudience
            ]);

            if ($success) {
                $id = $this->db->lastInsertId();
                return $this->sendSuccess(['id' => $id], 'Popup created successfully');
            } else {
                return $this->sendError('Failed to create popup', 500);
            }
        } catch (Exception $e) {
            error_log('创建弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to create popup: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取活跃的弹窗（供app使用）
     */
    private function getActivePopup() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            // 获取状态为active的弹窗，按优先级和创建时间排序
            $stmt = $this->db->prepare("SELECT * FROM app_popups WHERE status = 'active' ORDER BY priority DESC, created_at DESC LIMIT 1");
            $stmt->execute();
            $popup = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$popup) {
                return $this->sendSuccess(['popup' => null], 'No active popup found');
            }

            // 处理数据格式
            if (isset($popup['type'])) {
                if (is_string($popup['type']) && (strpos($popup['type'], '[') === 0 || strpos($popup['type'], '{') === 0)) {
                    $popup['type'] = json_decode($popup['type'], true);
                    if (!is_array($popup['type'])) {
                        $popup['type'] = [$popup['type']];
                    }
                } else {
                    $popup['type'] = is_array($popup['type']) ? $popup['type'] : [$popup['type']];
                }
            }

            // 确保数值字段为整数
            $popup['id'] = (int)$popup['id'];
            $popup['priority'] = (int)($popup['priority'] ?? 1);
            $popup['display_count'] = (int)($popup['display_count'] ?? 0);
            $popup['click_count'] = (int)($popup['click_count'] ?? 0);
            $popup['custom_days'] = $popup['custom_days'] ? (int)$popup['custom_days'] : null;

            // 增加显示次数
            $updateStmt = $this->db->prepare("UPDATE app_popups SET display_count = display_count + 1 WHERE id = ?");
            $updateStmt->execute([$popup['id']]);

            return $this->sendSuccess(['popup' => $popup], 'Active popup found');
        } catch (Exception $e) {
            error_log('获取活跃弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to get active popup: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 记录弹窗操作日志
     */
    private function logPopupAction() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $data = $this->getRequestData();

            if (empty($data['popup_id']) || empty($data['action'])) {
                return $this->sendError('Missing required fields: popup_id and action', 400);
            }

            $popupId = intval($data['popup_id']);
            $action = $data['action'];
            $deviceId = $data['device_id'] ?? null;
            $userAgent = $data['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? null;
            $ipAddress = $data['ip_address'] ?? $_SERVER['REMOTE_ADDR'] ?? null;

            // 验证操作类型
            $validActions = ['show', 'close', 'click', 'confirm'];
            if (!in_array($action, $validActions)) {
                return $this->sendError('Invalid action: ' . $action, 400);
            }

            // 插入日志记录
            $sql = "INSERT INTO popup_display_logs (popup_id, device_id, user_agent, ip_address, action) VALUES (?, ?, ?, ?, ?)";
            $stmt = $this->db->prepare($sql);
            $success = $stmt->execute([$popupId, $deviceId, $userAgent, $ipAddress, $action]);

            if ($success) {
                // 如果是点击操作，更新弹窗的点击次数
                if ($action === 'click' || $action === 'confirm') {
                    $updateStmt = $this->db->prepare("UPDATE app_popups SET click_count = click_count + 1 WHERE id = ?");
                    $updateStmt->execute([$popupId]);
                }

                return $this->sendSuccess(['log_id' => $this->db->lastInsertId()], 'Action logged successfully');
            } else {
                return $this->sendError('Failed to log action', 500);
            }
        } catch (Exception $e) {
            error_log('记录弹窗操作失败: ' . $e->getMessage());
            return $this->sendError('Failed to log action: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除单个弹窗
     */
    private function deletePopup($id) {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            // 先检查弹窗是否存在
            $stmt = $this->db->prepare("SELECT id FROM app_popups WHERE id = ?");
            $stmt->execute([$id]);
            if (!$stmt->fetch()) {
                return $this->sendError('Popup not found', 404);
            }

            // 删除弹窗
            $stmt = $this->db->prepare("DELETE FROM app_popups WHERE id = ?");
            $success = $stmt->execute([$id]);

            if ($success) {
                // 同时删除相关日志
                try {
                    $stmt = $this->db->prepare("DELETE FROM popup_logs WHERE popup_id = ?");
                    $stmt->execute([$id]);
                } catch (Exception $e) {
                    // 日志删除失败不影响主删除操作
                    error_log('删除弹窗日志失败: ' . $e->getMessage());
                }

                return $this->sendSuccess(null, 'Popup deleted successfully');
            } else {
                return $this->sendError('Failed to delete popup', 500);
            }
        } catch (Exception $e) {
            error_log('删除弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to delete popup: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除所有弹窗
     */
    private function deleteAllPopups() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            // 获取删除前的数量
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM app_popups");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $deleted_count = $result['count'];

            // 删除所有弹窗
            $stmt = $this->db->query("DELETE FROM app_popups");
            
            if ($stmt) {
                // 同时删除所有弹窗日志
                try {
                    $this->db->query("DELETE FROM popup_logs");
                } catch (Exception $e) {
                    // 日志删除失败不影响主删除操作
                    error_log('删除所有弹窗日志失败: ' . $e->getMessage());
                }

                return $this->sendSuccess(['deleted_count' => $deleted_count], "Successfully deleted $deleted_count popup(s)");
            } else {
                return $this->sendError('Failed to delete popups', 500);
            }
        } catch (Exception $e) {
            error_log('删除所有弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to delete popups: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新弹窗
     */
    private function updatePopup($id) {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            // 先检查弹窗是否存在
            $stmt = $this->db->prepare("SELECT id FROM app_popups WHERE id = ?");
            $stmt->execute([$id]);
            if (!$stmt->fetch()) {
                return $this->sendError('Popup not found', 404);
            }

            $data = $this->getRequestData();

            // 调试信息
            error_log('Update popup data: ' . json_encode($data));

            if (empty($data['title']) || empty($data['content'])) {
                return $this->sendError('Missing required fields: title and content', 400);
            }

            // 处理type数组 - 兼容多种格式
            $types = [];
            if (isset($data['type'])) {
                if (is_array($data['type'])) {
                    $types = $data['type'];
                } else if (is_string($data['type'])) {
                    // 如果是字符串，尝试解析为数组
                    $types = [$data['type']];
                }
            }

            // 如果没有找到 type，检查是否有 type[] 格式的数据
            if (empty($types) && isset($data['type[]'])) {
                $types = is_array($data['type[]']) ? $data['type[]'] : [$data['type[]']];
            }

            if (empty($types)) {
                return $this->sendError('Missing required field: type', 400);
            }

            // 验证类型
            $validTypes = ['once', 'daily_7', 'weekly', 'always', 'custom'];
            foreach ($types as $type) {
                $type = trim($type);
                if (!in_array($type, $validTypes)) {
                    return $this->sendError('Invalid type: ' . $type, 400);
                }
            }

            // 处理自定义天数
            $customDays = null;
            if (in_array('custom', $types) && isset($data['custom_days'])) {
                $customDays = intval($data['custom_days']);
                if ($customDays < 1 || $customDays > 365) {
                    return $this->sendError('Invalid custom days', 400);
                }
            }

            $stmt = $this->db->prepare("UPDATE app_popups SET title = ?, content = ?, type = ?, custom_days = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $success = $stmt->execute([
                $data['title'],
                $data['content'],
                json_encode($types),
                $customDays,
                $id
            ]);

            if ($success) {
                return $this->sendSuccess(['id' => $id], 'Popup updated successfully');
            } else {
                return $this->sendError('Failed to update popup', 500);
            }
        } catch (Exception $e) {
            error_log('更新弹窗失败: ' . $e->getMessage());
            return $this->sendError('Failed to update popup: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 处理协议相关请求
     */
    private function handleAgreement() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getAgreementList();
                    } else {
                        return $this->getAgreement($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request', 400);

            case 'POST':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'publish') {
                        // 发布协议: POST /agreement/publish
                        $data = $this->getRequestData();
                        if (isset($data['id'])) {
                            return $this->publishAgreement($data['id']);
                        } else {
                            return $this->sendError('Missing agreement ID', 400);
                        }
                    } else {
                        // 更新协议: POST /agreement/{id}
                        return $this->updateAgreement($this->params[0]);
                    }
                } else {
                    // 创建协议: POST /agreement
                    return $this->createAgreement();
                }

            case 'PUT':
                if (isset($this->params[0])) {
                    return $this->updateAgreement($this->params[0]);
                }
                return $this->sendError('Missing agreement ID', 400);

            case 'DELETE':
                if (isset($this->params[0])) {
                    return $this->deleteAgreement($this->params[0]);
                }
                return $this->sendError('Missing agreement ID', 400);

            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 获取协议列表
     */
    private function getAgreementList() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            // 获取所有协议，按排序字段和创建时间排序
            $stmt = $this->db->query("SELECT * FROM app_agreements ORDER BY
                CASE WHEN status = 'published' THEN 0 ELSE 1 END,
                sort_order ASC,
                created_at ASC");
            $agreements = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 同时返回已发布的协议列表供app使用
            $publishedAgreements = array_filter($agreements, function($agreement) {
                return $agreement['status'] === 'published';
            });

            return $this->sendSuccess([
                'agreements' => $agreements,
                'published_agreements' => array_values($publishedAgreements)
            ]);
        } catch (Exception $e) {
            error_log('获取协议列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get agreement list', 500);
        }
    }

    /**
     * 获取单个协议
     */
    private function getAgreement($id) {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->prepare("SELECT * FROM app_agreements WHERE id = ?");
            $stmt->execute([$id]);
            $agreement = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($agreement) {
                return $this->sendSuccess(['agreement' => $agreement]);
            } else {
                return $this->sendError('Agreement not found', 404);
            }
        } catch (Exception $e) {
            error_log('获取协议失败: ' . $e->getMessage());
            return $this->sendError('Failed to get agreement', 500);
        }
    }

    /**
     * 创建协议
     */
    private function createAgreement() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $data = $this->getRequestData();

            // 验证必填字段
            if (empty($data['title']) || empty($data['content'])) {
                return $this->sendError('Missing required fields: title, content', 400);
            }

            // 准备数据
            $title = trim($data['title']);
            $content = $data['content'];
            $type = isset($data['type']) ? $data['type'] : 'privacy';
            $version = isset($data['version']) ? $data['version'] : '1.0';
            $status = isset($data['status']) ? $data['status'] : 'draft';
            $sortOrder = isset($data['sort_order']) ? intval($data['sort_order']) : 0;

            // 验证协议类型
            $validTypes = ['privacy', 'terms', 'service', 'user'];
            if (!in_array($type, $validTypes)) {
                $type = 'privacy';
            }

            // 验证状态值
            if (!in_array($status, ['draft', 'published'])) {
                $status = 'draft';
            }

            // 如果没有指定排序，自动设置为当前最大值+1
            if ($sortOrder <= 0) {
                $stmt = $this->db->query("SELECT COALESCE(MAX(sort_order), 0) + 1 as next_order FROM app_agreements");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $sortOrder = $result['next_order'];
            }

            // 插入数据
            $stmt = $this->db->prepare("INSERT INTO app_agreements (title, content, type, version, status, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
            $success = $stmt->execute([$title, $content, $type, $version, $status, $sortOrder]);

            if ($success) {
                $id = $this->db->lastInsertId();

                // 获取创建的协议信息
                $stmt = $this->db->prepare("SELECT * FROM app_agreements WHERE id = ?");
                $stmt->execute([$id]);
                $agreement = $stmt->fetch(PDO::FETCH_ASSOC);

                return $this->sendSuccess([
                    'id' => $id,
                    'agreement' => $agreement
                ], 'Agreement created successfully');
            } else {
                return $this->sendError('Failed to create agreement', 500);
            }
        } catch (Exception $e) {
            error_log('创建协议失败: ' . $e->getMessage());
            return $this->sendError('Failed to create agreement: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 更新协议
     */
    private function updateAgreement($id) {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $data = $this->getRequestData();

            // 验证必填字段
            if (empty($data['title']) || empty($data['content'])) {
                return $this->sendError('Missing required fields: title, content', 400);
            }

            // 准备数据
            $title = trim($data['title']);
            $content = $data['content'];
            $type = isset($data['type']) ? $data['type'] : 'privacy';
            $version = isset($data['version']) ? $data['version'] : '1.0';
            $status = isset($data['status']) ? $data['status'] : 'draft';
            $sortOrder = isset($data['sort_order']) ? intval($data['sort_order']) : null;

            // 验证协议类型
            $validTypes = ['privacy', 'terms', 'service', 'user'];
            if (!in_array($type, $validTypes)) {
                $type = 'privacy';
            }

            // 验证状态值
            if (!in_array($status, ['draft', 'published'])) {
                $status = 'draft';
            }

            // 更新数据
            if ($sortOrder !== null) {
                $stmt = $this->db->prepare("UPDATE app_agreements SET title = ?, content = ?, type = ?, version = ?, status = ?, sort_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $success = $stmt->execute([$title, $content, $type, $version, $status, $sortOrder, $id]);
            } else {
                $stmt = $this->db->prepare("UPDATE app_agreements SET title = ?, content = ?, type = ?, version = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $success = $stmt->execute([$title, $content, $type, $version, $status, $id]);
            }

            if ($success) {
                // 获取更新后的协议信息
                $stmt = $this->db->prepare("SELECT * FROM app_agreements WHERE id = ?");
                $stmt->execute([$id]);
                $agreement = $stmt->fetch(PDO::FETCH_ASSOC);

                return $this->sendSuccess([
                    'agreement' => $agreement
                ], 'Agreement updated successfully');
            } else {
                return $this->sendError('Failed to update agreement', 500);
            }
        } catch (Exception $e) {
            error_log('更新协议失败: ' . $e->getMessage());
            return $this->sendError('Failed to update agreement: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除协议
     */
    private function deleteAgreement($id) {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->prepare("DELETE FROM app_agreements WHERE id = ?");
            $success = $stmt->execute([$id]);

            if ($success) {
                return $this->sendSuccess(null, 'Agreement deleted successfully');
            } else {
                return $this->sendError('Failed to delete agreement', 500);
            }
        } catch (Exception $e) {
            error_log('删除协议失败: ' . $e->getMessage());
            return $this->sendError('Failed to delete agreement: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 发布协议
     */
    private function publishAgreement($id) {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            // 发布指定协议（支持多个协议同时发布）
            $stmt = $this->db->prepare("UPDATE app_agreements SET status = 'published', updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $success = $stmt->execute([$id]);

            if ($success) {
                // 获取发布的协议信息
                $stmt = $this->db->prepare("SELECT * FROM app_agreements WHERE id = ?");
                $stmt->execute([$id]);
                $agreement = $stmt->fetch(PDO::FETCH_ASSOC);

                return $this->sendSuccess([
                    'agreement' => $agreement
                ], 'Agreement published successfully');
            } else {
                return $this->sendError('Failed to publish agreement', 500);
            }
        } catch (Exception $e) {
            error_log('发布协议失败: ' . $e->getMessage());
            return $this->sendError('Failed to publish agreement: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 处理更新相关请求
     */
    private function handleUpdate() {
        switch ($this->method) {
            case 'GET':
                if (isset($this->params[0])) {
                    if ($this->params[0] === 'list') {
                        return $this->getUpdateList();
                    } else {
                        return $this->getUpdate($this->params[0]);
                    }
                }
                return $this->sendError('Invalid request', 400);
                
            case 'POST':
                return $this->createUpdate();
                
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 获取更新列表
     */
    private function getUpdateList() {
        try {
            if (!$this->db) {
                return $this->sendError('Database not available', 500);
            }

            $stmt = $this->db->query("SELECT * FROM app_updates ORDER BY created_at DESC");
            $versions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return $this->sendSuccess(['versions' => $versions]);
        } catch (Exception $e) {
            error_log('获取更新列表失败: ' . $e->getMessage());
            return $this->sendError('Failed to get update list', 500);
        }
    }
    
    /**
     * 获取请求数据
     */
    private function getRequestData() {
        if ($this->method === 'GET') {
            return $_GET;
        } else if ($this->method === 'POST') {
            // POST 请求优先使用 $_POST，然后尝试 JSON
            if (!empty($_POST)) {
                return $_POST;
            }
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            return $data ?: [];
        } else {
            // PUT/PATCH/DELETE 请求处理
            $input = file_get_contents('php://input');

            // 首先尝试解析 JSON
            $data = json_decode($input, true);
            if ($data !== null) {
                return $data;
            }

            // 如果不是 JSON，尝试解析 form-data
            // 对于 PUT 请求的 FormData，需要手动解析
            if (strpos($_SERVER['CONTENT_TYPE'] ?? '', 'multipart/form-data') !== false) {
                return $this->parseFormData($input);
            }

            // 尝试解析 URL 编码数据
            parse_str($input, $data);
            return $data ?: [];
        }
    }

    /**
     * 解析 FormData 格式的数据
     */
    private function parseFormData($input) {
        $data = [];

        // 获取 boundary
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (preg_match('/boundary=(.+)$/', $contentType, $matches)) {
            $boundary = $matches[1];

            // 分割数据
            $parts = explode('--' . $boundary, $input);

            foreach ($parts as $part) {
                if (trim($part) === '' || trim($part) === '--') {
                    continue;
                }

                // 分离头部和内容
                $sections = explode("\r\n\r\n", $part, 2);
                if (count($sections) !== 2) {
                    continue;
                }

                $headers = $sections[0];
                $content = rtrim($sections[1], "\r\n");

                // 解析 name 属性
                if (preg_match('/name="([^"]+)"/', $headers, $nameMatches)) {
                    $name = $nameMatches[1];

                    // 处理数组字段（如 type[]）
                    if (substr($name, -2) === '[]') {
                        $arrayName = substr($name, 0, -2);
                        if (!isset($data[$arrayName])) {
                            $data[$arrayName] = [];
                        }
                        $data[$arrayName][] = $content;
                    } else {
                        $data[$name] = $content;
                    }
                }
            }
        }

        return $data;
    }







    /**
     * 发送成功响应
     */
    private function sendSuccess($data = null, $message = 'Success') {
        $response = ['success' => true, 'message' => $message];
        if ($data) {
            $response = array_merge($response, $data);
        }
        echo json_encode($response);
        return true;
    }
    
    /**
     * 发送错误响应
     */
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode(['success' => false, 'message' => $message]);
        return false;
    }
}

// 处理请求
try {
    $api = new AppSettingsAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
    error_log('APP Settings API Fatal Error: ' . $e->getMessage());
}
?>