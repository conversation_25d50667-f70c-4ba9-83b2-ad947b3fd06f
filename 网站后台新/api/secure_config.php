<?php
/**
 * 安全配置类
 * 用于加密和解密敏感配置信息
 * 
 * @version 1.0.0
 */

class SecureConfig {
    private static $instance = null;
    private $encryptionKey;
    private $cipher = 'AES-256-CBC';
    
    /**
     * 构造函数
     */
    private function __construct() {
        // 使用服务器特定信息生成加密密钥
        $serverKey = $this->getServerSpecificKey();
        $this->encryptionKey = hash('sha256', $serverKey, true);
    }
    
    /**
     * 获取实例（单例模式）
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 获取服务器特定的密钥
     * 使用服务器信息和文件路径生成唯一密钥
     */
    private function getServerSpecificKey() {
        // 组合多个服务器特定的信息
        $serverInfo = [
            $_SERVER['DOCUMENT_ROOT'] ?? '',
            $_SERVER['SERVER_ADDR'] ?? '',
            $_SERVER['SERVER_NAME'] ?? '',
            dirname(__DIR__), // 项目根目录
            filemtime(__FILE__) // 文件修改时间作为额外的熵
        ];
        
        // 生成一个基于服务器信息的密钥
        return hash('sha256', implode('|', $serverInfo));
    }
    
    /**
     * 加密数据
     * 
     * @param string $data 要加密的数据
     * @return string 加密后的数据（base64编码）
     */
    public function encrypt($data) {
        if (empty($data)) {
            return '';
        }
        
        $ivSize = openssl_cipher_iv_length($this->cipher);
        $iv = openssl_random_pseudo_bytes($ivSize);
        
        $encrypted = openssl_encrypt(
            $data,
            $this->cipher,
            $this->encryptionKey,
            OPENSSL_RAW_DATA,
            $iv
        );
        
        if ($encrypted === false) {
            error_log('加密失败: ' . openssl_error_string());
            return '';
        }
        
        // 将IV和加密数据合并并进行base64编码
        $combined = $iv . $encrypted;
        return base64_encode($combined);
    }
    
    /**
     * 解密数据
     * 
     * @param string $data 要解密的数据（base64编码）
     * @return string 解密后的数据
     */
    public function decrypt($data) {
        if (empty($data)) {
            return '';
        }
        
        $combined = base64_decode($data);
        if ($combined === false) {
            error_log('解密失败: 无效的base64编码');
            return '';
        }
        
        $ivSize = openssl_cipher_iv_length($this->cipher);
        if (strlen($combined) <= $ivSize) {
            error_log('解密失败: 数据长度不足');
            return '';
        }
        
        // 分离IV和加密数据
        $iv = substr($combined, 0, $ivSize);
        $encrypted = substr($combined, $ivSize);
        
        $decrypted = openssl_decrypt(
            $encrypted,
            $this->cipher,
            $this->encryptionKey,
            OPENSSL_RAW_DATA,
            $iv
        );
        
        if ($decrypted === false) {
            error_log('解密失败: ' . openssl_error_string());
            return '';
        }
        
        return $decrypted;
    }
    
    /**
     * 安全保存配置到文件
     * 
     * @param array $config 配置数组
     * @param string $filePath 文件路径
     * @return bool 是否保存成功
     */
    public function saveConfig($config, $filePath) {
        // 加密敏感字段
        $sensitiveFields = ['DB_PASS', 'API_JWT_SECRET', 'password', 'secret'];
        $encryptedConfig = [];
        
        foreach ($config as $key => $value) {
            // 检查是否为敏感字段
            $isSensitive = false;
            foreach ($sensitiveFields as $field) {
                if (stripos($key, $field) !== false) {
                    $isSensitive = true;
                    break;
                }
            }
            
            // 加密敏感字段
            if ($isSensitive && !empty($value)) {
                $encryptedConfig[$key] = 'ENC:' . $this->encrypt($value);
            } else {
                $encryptedConfig[$key] = $value;
            }
        }
        
        // 转换为JSON并保存
        $jsonData = json_encode($encryptedConfig, JSON_PRETTY_PRINT);
        return file_put_contents($filePath, $jsonData) !== false;
    }
    
    /**
     * 从文件加载配置
     * 
     * @param string $filePath 文件路径
     * @return array 配置数组
     */
    public function loadConfig($filePath) {
        if (!file_exists($filePath)) {
            return [];
        }
        
        $jsonData = file_get_contents($filePath);
        if ($jsonData === false) {
            return [];
        }
        
        $config = json_decode($jsonData, true);
        if (!is_array($config)) {
            return [];
        }
        
        // 解密敏感字段
        foreach ($config as $key => $value) {
            if (is_string($value) && strpos($value, 'ENC:') === 0) {
                $encryptedValue = substr($value, 4);
                $config[$key] = $this->decrypt($encryptedValue);
            }
        }
        
        return $config;
    }
}
?> 