<?php
/**
 * 根据卡密功能获取对应的脚本
 * 实现卡密功能与脚本类型的绑定机制
 */

require_once '../includes/db.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

function respondError($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'error' => $message,
        'code' => $code
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

function respondSuccess($data) {
    echo json_encode([
        'success' => true,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 获取卡密值
    $key_value = $_GET['key'] ?? $_POST['key'] ?? '';
    
    if (empty($key_value)) {
        respondError('卡密不能为空');
    }
    
    // 验证卡密并获取功能权限
    $stmt = $pdo->prepare("
        SELECT 
            id,
            key_value,
            has_customer_service,
            has_product_listing,
            status,
            expiry_date,
            store_name,
            wechat_store_id
        FROM license_keys 
        WHERE key_value = ? AND status = 'active' AND expiry_date > NOW()
    ");
    $stmt->execute([$key_value]);
    $key_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$key_data) {
        respondError('卡密无效或已过期', 403);
    }
    
    // 根据卡密功能获取对应的脚本
    $scripts = [];
    
    // 构建脚本查询条件
    $script_conditions = [];
    $script_params = [];
    
    // 如果卡密有微信小店功能，获取微信小店脚本
    if ($key_data['has_customer_service']) {
        $script_conditions[] = "has_wechat_store = 1";
    }
    
    // 如果卡密有抖店功能，获取抖店脚本
    if ($key_data['has_product_listing']) {
        $script_conditions[] = "has_douyin_store = 1";
    }
    
    if (empty($script_conditions)) {
        respondError('该卡密没有任何功能权限');
    }
    
    // 查询对应的脚本
    $script_sql = "
        SELECT 
            id,
            name,
            version,
            description,
            script_code,
            has_wechat_store,
            has_douyin_store,
            created_at,
            updated_at
        FROM scripts 
        WHERE status = 'active' AND (" . implode(' OR ', $script_conditions) . ")
        ORDER BY updated_at DESC
    ";
    
    $stmt = $pdo->prepare($script_sql);
    $stmt->execute($script_params);
    $scripts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 为每个脚本添加类型标识
    foreach ($scripts as &$script) {
        $script['script_types'] = [];
        if ($script['has_wechat_store']) {
            $script['script_types'][] = 'wechat_store';
        }
        if ($script['has_douyin_store']) {
            $script['script_types'][] = 'douyin_store';
        }
        
        // 检查卡密是否有权限使用此脚本
        $script['accessible'] = false;
        if ($script['has_wechat_store'] && $key_data['has_customer_service']) {
            $script['accessible'] = true;
        }
        if ($script['has_douyin_store'] && $key_data['has_product_listing']) {
            $script['accessible'] = true;
        }
    }
    
    // 过滤掉不可访问的脚本
    $scripts = array_filter($scripts, function($script) {
        return $script['accessible'];
    });
    
    // 重新索引数组
    $scripts = array_values($scripts);
    
    // 返回结果
    respondSuccess([
        'key_info' => [
            'key_value' => $key_data['key_value'],
            'has_customer_service' => (bool)$key_data['has_customer_service'],
            'has_product_listing' => (bool)$key_data['has_product_listing'],
            'store_name' => $key_data['store_name'],
            'wechat_store_id' => $key_data['wechat_store_id'],
            'expiry_date' => $key_data['expiry_date']
        ],
        'available_scripts' => $scripts,
        'script_count' => count($scripts),
        'function_mapping' => [
            'has_customer_service' => 'wechat_store',
            'has_product_listing' => 'douyin_store'
        ]
    ]);
    
} catch (Exception $e) {
    error_log("获取脚本失败: " . $e->getMessage());
    respondError('服务器内部错误', 500);
}
?>
