<?php
/**
 * API密钥自动更新定时任务脚本
 * 
 * 此脚本用于通过cron作业定期执行API密钥的自动更新
 * 推荐的cron设置：每天凌晨3点执行一次
 * 0 3 * * * php /path/to/cron_auto_update_keys.php > /dev/null 2>&1
 */

// 设置执行时间限制和内存限制
set_time_limit(300); // 5分钟
ini_set('memory_limit', '128M');

// 严格的错误控制
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/cron_auto_update.log');

// 记录开始执行时间
$startTime = microtime(true);
$logPrefix = '[' . date('Y-m-d H:i:s') . '] ';

// 记录开始日志
error_log($logPrefix . "开始执行API密钥自动更新定时任务");

// 引入API密钥管理器
require_once __DIR__ . '/key_manager.php';

try {
    // 创建API密钥管理器
    $manager = new ApiKeyManager();
    
    // 执行自动更新
    $result = $manager->runAutoUpdate();
    
    // 记录结果
    if ($result['success']) {
        error_log($logPrefix . $result['message']);
        
        // 记录详细信息
        if (!empty($result['details'])) {
            foreach ($result['details'] as $detail) {
                $status = $detail['status'] === 'success' ? '成功' : '失败';
                $message = $detail['status'] === 'success' 
                    ? "下次更新时间: {$detail['next_update']}" 
                    : "错误信息: {$detail['message']}";
                
                error_log($logPrefix . "令牌 ID: {$detail['token_id']}, 类型: {$detail['token_type']}, 状态: {$status}, {$message}");
            }
        }
    } else {
        error_log($logPrefix . "自动更新失败: " . $result['message']);
    }
} catch (Exception $e) {
    error_log($logPrefix . "执行过程中发生错误: " . $e->getMessage());
}

// 记录执行时间
$executionTime = microtime(true) - $startTime;
error_log($logPrefix . "API密钥自动更新定时任务完成，耗时: " . round($executionTime, 2) . " 秒");

// 如果是通过命令行运行，输出结果
if (php_sapi_name() === 'cli') {
    echo "API密钥自动更新定时任务完成，耗时: " . round($executionTime, 2) . " 秒\n";
    
    // 如果有详细结果，也输出
    if (isset($result) && $result['success']) {
        echo $result['message'] . "\n";
    }
} 