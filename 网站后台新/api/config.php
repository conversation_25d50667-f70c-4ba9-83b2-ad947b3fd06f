<?php
/**
 * API配置类
 * 管理API配置和环境变量
 * 
 * @version 1.0.0
 */

class ApiConfig {
    private static $instance = null;
    private $config = [];
    private $dbConfig = [];
    private $envFile = null;
    private $isLoaded = false;

    /**
     * 构造函数
     */
    public function __construct() {
        $this->envFile = dirname(__DIR__) . '/.env';
        $this->loadConfig();
    }

    /**
     * 获取实例（单例模式）
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 加载配置
     */
    private function loadConfig() {
        if ($this->isLoaded) {
            return;
        }

        // 默认配置
        $this->config = [
            'api_version' => 'v2',
            'api_secret_key' => 'xiaomeihua_default_key_change_this',
            'enable_signature_validation' => true,
            'enable_timestamp_validation' => true,
            'enable_token_validation' => true,
            'timestamp_ttl' => 300, // 5分钟
            'token_ttl' => 3600, // 1小时
            'debug_mode' => false,
            'log_api_requests' => true,
            'allowed_origins' => ['*'],
            'rate_limit_enabled' => true,
            'rate_limit_requests' => 60, // 每分钟请求数
            'rate_limit_window' => 60, // 时间窗口（秒）
            'encryption_enabled' => true,
            'encryption_algorithm' => 'aes-256-gcm',
            'dynamic_endpoint_enabled' => true
        ];

        // 数据库默认配置 - 使用您提供的服务器信息
        $this->dbConfig = [
            'host' => '127.0.0.1',
            'port' => '3306',
            'dbname' => 'xiaomeihuakefu_c',
            'user' => 'xiaomeihuakefu_c',
            'pass' => '7Da5F1Xx995cxYz8'
        ];

        // 加载.env文件
        $this->loadEnvFile();

        // 从环境变量加载配置
        $this->loadFromEnv();

        // 从数据库加载配置
        $this->loadFromDatabase();

        $this->isLoaded = true;
    }

    /**
     * 加载.env文件
     */
    private function loadEnvFile() {
        if (file_exists($this->envFile)) {
            $lines = file($this->envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                // 忽略注释
                if (strpos(trim($line), '#') === 0) {
                    continue;
                }

                // 解析键值对
                list($name, $value) = explode('=', $line, 2);
                $name = trim($name);
                $value = trim($value);

                // 去除引号
                if (strpos($value, '"') === 0 && strrpos($value, '"') === strlen($value) - 1) {
                    $value = substr($value, 1, -1);
                } elseif (strpos($value, "'") === 0 && strrpos($value, "'") === strlen($value) - 1) {
                    $value = substr($value, 1, -1);
                }

                // 设置环境变量
                putenv("$name=$value");
                $_ENV[$name] = $value;
                $_SERVER[$name] = $value;
            }
        }
    }

    /**
     * 从环境变量加载配置
     */
    private function loadFromEnv() {
        // 加载API配置
        $this->config['api_version'] = getenv('API_VERSION') ?: $this->config['api_version'];
        $this->config['api_secret_key'] = getenv('API_SECRET_KEY') ?: $this->config['api_secret_key'];
        $this->config['enable_signature_validation'] = $this->getBoolEnv('ENABLE_SIGNATURE_VALIDATION', $this->config['enable_signature_validation']);
        $this->config['enable_timestamp_validation'] = $this->getBoolEnv('ENABLE_TIMESTAMP_VALIDATION', $this->config['enable_timestamp_validation']);
        $this->config['enable_token_validation'] = $this->getBoolEnv('ENABLE_TOKEN_VALIDATION', $this->config['enable_token_validation']);
        $this->config['timestamp_ttl'] = getenv('TIMESTAMP_TTL') ? (int)getenv('TIMESTAMP_TTL') : $this->config['timestamp_ttl'];
        $this->config['token_ttl'] = getenv('TOKEN_TTL') ? (int)getenv('TOKEN_TTL') : $this->config['token_ttl'];
        $this->config['debug_mode'] = $this->getBoolEnv('DEBUG_MODE', $this->config['debug_mode']);
        $this->config['log_api_requests'] = $this->getBoolEnv('LOG_API_REQUESTS', $this->config['log_api_requests']);
        $this->config['rate_limit_enabled'] = $this->getBoolEnv('RATE_LIMIT_ENABLED', $this->config['rate_limit_enabled']);
        $this->config['rate_limit_requests'] = getenv('RATE_LIMIT_REQUESTS') ? (int)getenv('RATE_LIMIT_REQUESTS') : $this->config['rate_limit_requests'];
        $this->config['rate_limit_window'] = getenv('RATE_LIMIT_WINDOW') ? (int)getenv('RATE_LIMIT_WINDOW') : $this->config['rate_limit_window'];
        $this->config['encryption_enabled'] = $this->getBoolEnv('ENCRYPTION_ENABLED', $this->config['encryption_enabled']);
        $this->config['encryption_algorithm'] = getenv('ENCRYPTION_ALGORITHM') ?: $this->config['encryption_algorithm'];
        $this->config['dynamic_endpoint_enabled'] = $this->getBoolEnv('DYNAMIC_ENDPOINT_ENABLED', $this->config['dynamic_endpoint_enabled']);

        // 加载数据库配置
        $this->dbConfig['host'] = getenv('DB_HOST') ?: $this->dbConfig['host'];
        $this->dbConfig['port'] = getenv('DB_PORT') ?: $this->dbConfig['port'];
        $this->dbConfig['dbname'] = getenv('DB_NAME') ?: $this->dbConfig['dbname'];
        $this->dbConfig['user'] = getenv('DB_USER') ?: $this->dbConfig['user'];
        $this->dbConfig['pass'] = getenv('DB_PASS') ?: $this->dbConfig['pass'];
    }

    /**
     * 从数据库加载配置
     */
    private function loadFromDatabase() {
        try {
            // 连接数据库
            $dsn = "mysql:host={$this->dbConfig['host']};port={$this->dbConfig['port']};dbname={$this->dbConfig['dbname']};charset=utf8mb4";
            $pdo = new PDO($dsn, $this->dbConfig['user'], $this->dbConfig['pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);

            // 检查系统设置表是否存在
            $stmt = $pdo->query("SHOW TABLES LIKE 'system_settings'");
            if ($stmt->rowCount() > 0) {
                // 加载系统设置
                $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings WHERE setting_key LIKE 'api_%'");
                $settings = $stmt->fetchAll();

                foreach ($settings as $setting) {
                    $key = str_replace('api_', '', $setting['setting_key']);
                    if (array_key_exists($key, $this->config)) {
                        // 根据类型转换值
                        $value = $setting['setting_value'];
                        if (is_numeric($value)) {
                            $value = $value + 0; // 转换为数字
                        } elseif ($value === 'true' || $value === 'false') {
                            $value = $value === 'true';
                        }
                        $this->config[$key] = $value;
                    }
                }
            }
        } catch (PDOException $e) {
            // 数据库连接失败，使用默认配置
            error_log("无法从数据库加载配置: " . $e->getMessage());
        }
    }

    /**
     * 获取布尔类型的环境变量
     */
    private function getBoolEnv($name, $default) {
        $value = getenv($name);
        if ($value === false) {
            return $default;
        }
        return in_array(strtolower($value), ['true', '1', 'yes', 'on']);
    }

    /**
     * 获取配置值
     */
    public function get($key, $default = null) {
        return isset($this->config[$key]) ? $this->config[$key] : $default;
    }

    /**
     * 设置配置值
     */
    public function set($key, $value) {
        $this->config[$key] = $value;
        return $this;
    }

    /**
     * 获取所有配置
     */
    public function getAll() {
        return $this->config;
    }

    /**
     * 获取数据库配置
     */
    public function getDatabaseConfig() {
        return $this->dbConfig;
    }

    /**
     * 获取API密钥
     */
    public function getApiSecretKey() {
        return $this->config['api_secret_key'];
    }

    /**
     * 是否启用签名验证
     */
    public function isSignatureValidationEnabled() {
        return $this->config['enable_signature_validation'];
    }

    /**
     * 是否启用时间戳验证
     */
    public function isTimestampValidationEnabled() {
        return $this->config['enable_timestamp_validation'];
    }

    /**
     * 是否启用令牌验证
     */
    public function isTokenValidationEnabled() {
        return $this->config['enable_token_validation'];
    }

    /**
     * 获取时间戳有效期
     */
    public function getTimestampTtl() {
        return $this->config['timestamp_ttl'];
    }

    /**
     * 获取令牌有效期
     */
    public function getTokenTtl() {
        return $this->config['token_ttl'];
    }

    /**
     * 是否启用调试模式
     */
    public function isDebugMode() {
        return $this->config['debug_mode'];
    }

    /**
     * 是否记录API请求
     */
    public function isLogApiRequests() {
        return $this->config['log_api_requests'];
    }

    /**
     * 保存配置到数据库
     */
    public function saveToDatabase($key, $value) {
        try {
            // 连接数据库
            $dsn = "mysql:host={$this->dbConfig['host']};port={$this->dbConfig['port']};dbname={$this->dbConfig['dbname']};charset=utf8mb4";
            $pdo = new PDO($dsn, $this->dbConfig['user'], $this->dbConfig['pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);

            // 检查系统设置表是否存在
            $stmt = $pdo->query("SHOW TABLES LIKE 'system_settings'");
            if ($stmt->rowCount() === 0) {
                // 创建系统设置表
                $pdo->exec("
                    CREATE TABLE system_settings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        setting_key VARCHAR(50) NOT NULL UNIQUE,
                        setting_value TEXT,
                        description TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                ");
            }

            // 保存设置
            $stmt = $pdo->prepare("
                INSERT INTO system_settings (setting_key, setting_value) 
                VALUES (?, ?) 
                ON DUPLICATE KEY UPDATE setting_value = ?
            ");
            $stmt->execute(["api_{$key}", $value, $value]);

            // 更新内存中的配置
            $this->config[$key] = $value;

            return true;
        } catch (PDOException $e) {
            error_log("保存配置到数据库失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 保存数据库配置到.env文件
     */
    public function saveDatabaseConfig($config) {
        // 更新内存中的配置
        foreach ($config as $key => $value) {
            if (array_key_exists($key, $this->dbConfig)) {
                $this->dbConfig[$key] = $value;
            }
        }

        // 更新.env文件
        $envContent = '';
        if (file_exists($this->envFile)) {
            $lines = file($this->envFile, FILE_IGNORE_NEW_LINES);
            $updated = [
                'DB_HOST' => false,
                'DB_PORT' => false,
                'DB_NAME' => false,
                'DB_USER' => false,
                'DB_PASS' => false
            ];

            foreach ($lines as $line) {
                if (empty(trim($line)) || strpos(trim($line), '#') === 0) {
                    $envContent .= $line . PHP_EOL;
                    continue;
                }

                $parts = explode('=', $line, 2);
                if (count($parts) !== 2) {
                    $envContent .= $line . PHP_EOL;
                    continue;
                }

                $key = trim($parts[0]);
                $value = trim($parts[1]);

                switch ($key) {
                    case 'DB_HOST':
                        $value = $this->dbConfig['host'];
                        $updated['DB_HOST'] = true;
                        break;
                    case 'DB_PORT':
                        $value = $this->dbConfig['port'];
                        $updated['DB_PORT'] = true;
                        break;
                    case 'DB_NAME':
                        $value = $this->dbConfig['dbname'];
                        $updated['DB_NAME'] = true;
                        break;
                    case 'DB_USER':
                        $value = $this->dbConfig['user'];
                        $updated['DB_USER'] = true;
                        break;
                    case 'DB_PASS':
                        $value = $this->dbConfig['pass'];
                        $updated['DB_PASS'] = true;
                        break;
                }

                $envContent .= "{$key}={$value}" . PHP_EOL;
            }

            // 添加缺失的配置
            foreach ($updated as $key => $isUpdated) {
                if (!$isUpdated) {
                    $value = '';
                    switch ($key) {
                        case 'DB_HOST':
                            $value = $this->dbConfig['host'];
                            break;
                        case 'DB_PORT':
                            $value = $this->dbConfig['port'];
                            break;
                        case 'DB_NAME':
                            $value = $this->dbConfig['dbname'];
                            break;
                        case 'DB_USER':
                            $value = $this->dbConfig['user'];
                            break;
                        case 'DB_PASS':
                            $value = $this->dbConfig['pass'];
                            break;
                    }
                    $envContent .= "{$key}={$value}" . PHP_EOL;
                }
            }
        } else {
            // 创建新的.env文件
            $envContent = "# 数据库配置" . PHP_EOL;
            $envContent .= "DB_HOST={$this->dbConfig['host']}" . PHP_EOL;
            $envContent .= "DB_PORT={$this->dbConfig['port']}" . PHP_EOL;
            $envContent .= "DB_NAME={$this->dbConfig['dbname']}" . PHP_EOL;
            $envContent .= "DB_USER={$this->dbConfig['user']}" . PHP_EOL;
            $envContent .= "DB_PASS={$this->dbConfig['pass']}" . PHP_EOL;
            $envContent .= PHP_EOL;
            $envContent .= "# API配置" . PHP_EOL;
            $envContent .= "API_VERSION={$this->config['api_version']}" . PHP_EOL;
            $envContent .= "API_SECRET_KEY={$this->config['api_secret_key']}" . PHP_EOL;
            $envContent .= "ENABLE_SIGNATURE_VALIDATION=" . ($this->config['enable_signature_validation'] ? 'true' : 'false') . PHP_EOL;
            $envContent .= "ENABLE_TIMESTAMP_VALIDATION=" . ($this->config['enable_timestamp_validation'] ? 'true' : 'false') . PHP_EOL;
            $envContent .= "ENABLE_TOKEN_VALIDATION=" . ($this->config['enable_token_validation'] ? 'true' : 'false') . PHP_EOL;
        }

        // 保存.env文件
        if (file_put_contents($this->envFile, $envContent) === false) {
            error_log("保存.env文件失败");
            return false;
        }

        return true;
    }

    /**
     * 获取数据库连接
     */
    public function getDatabaseConnection() {
        $dsn = "mysql:host={$this->dbConfig['host']};port={$this->dbConfig['port']};dbname={$this->dbConfig['dbname']};charset=utf8mb4";
        return new PDO($dsn, $this->dbConfig['user'], $this->dbConfig['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]);
    }
} 