<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db.php';
require_once '../includes/functions.php';

// 禁用错误显示到页面，但记录到日志
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 【修复】增强的API调用日志记录
function logApiCall($key, $ip, $status, $message) {
    global $pdo;
    try {
        // 【修复】使用预处理语句防止SQL注入
        $stmt = $pdo->prepare("INSERT INTO api_logs (license_key, ip_address, status, message, created_at) VALUES (?, ?, ?, ?, NOW())");
        // 过滤和截断输入数据
        $key = substr(trim($key), 0, 255);
        $ip = substr(trim($ip), 0, 45);
        $status = substr(trim($status), 0, 20);
        $message = substr($message, 0, 1000);
        
        $stmt->execute([$key, $ip, $status, $message]);
    } catch (Exception $e) {
        // 如果api_logs表不存在，尝试创建
        try {
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS api_logs (
                    id int(11) NOT NULL AUTO_INCREMENT,
                    license_key varchar(255) DEFAULT NULL,
                    ip_address varchar(45) DEFAULT NULL,
                    status varchar(20) DEFAULT NULL,
                    message text DEFAULT NULL,
                    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    KEY idx_license_key (license_key),
                    KEY idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
            // 再次尝试插入日志
            $stmt = $pdo->prepare("INSERT INTO api_logs (license_key, ip_address, status, message, created_at) VALUES (?, ?, ?, ?, NOW())");
            $stmt->execute([$key, $ip, $status, $message]);
        } catch (Exception $e2) {
            // 记录到PHP错误日志
            error_log("API日志记录失败: " . $e2->getMessage());
        }
    }
}

// 【新增】数据库连接检查
function checkDatabaseConnection() {
    global $pdo;
    try {
        $pdo->query("SELECT 1");
        return true;
    } catch (Exception $e) {
        error_log("数据库连接检查失败: " . $e->getMessage());
        return false;
    }
}

// 【安全增强】过滤和验证输入数据
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    if (is_string($data)) {
        // 移除潜在的XSS和SQL注入代码
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        // 移除null字节
        $data = str_replace("\0", '', $data);
        // 限制长度
        $data = substr($data, 0, 1000);
    }
    
    return $data;
}

// 获取并过滤POST输入
$rawKey = $_POST['key'] ?? '';
$rawShopName = $_POST['shop_name'] ?? '';
$rawShopId = $_POST['shop_id'] ?? '';
$rawCheckStatus = $_POST['check_status'] ?? '';
$rawPhoneNumber = $_POST['phone_number'] ?? '';
$rawBindPhone = $_POST['bind_phone'] ?? '';
$rawLoginType = $_POST['login_type'] ?? '';

// 安全过滤输入
$key = sanitizeInput(trim($rawKey));
$shop_name = sanitizeInput(trim($rawShopName));
$shop_id = sanitizeInput(trim($rawShopId));
$check_status = sanitizeInput(trim($rawCheckStatus));
$phone_number = sanitizeInput(trim($rawPhoneNumber));
$bind_phone = intval($rawBindPhone);
$login_type = sanitizeInput(trim($rawLoginType));

// 获取客户端IP地址
$client_ip = get_client_ip();

// 【增强】记录请求信息，但不显示完整的卡密和手机号码
$masked_key = !empty($key) ? substr($key, 0, 4) . '****' . substr($key, -4) : '空';
$masked_phone = !empty($phone_number) ? substr($phone_number, 0, 3) . '****' . substr($phone_number, -4) : '';
error_log("API验证请求: key={$masked_key}, phone={$masked_phone}, login_type={$login_type}, bind_phone={$bind_phone}, IP={$client_ip}");

// 输入验证 - 支持手机号码登录
if ($login_type === 'phone') {
    // 手机号码登录模式
    if (empty($phone_number)) {
        logApiCall('', $client_ip, 'error', '手机号码不能为空');
        echo json_encode(['success' => false, 'message' => '手机号码不能为空']);
        exit();
    }

    // 验证手机号码格式
    if (!preg_match('/^1[3-9]\d{9}$/', $phone_number)) {
        logApiCall($phone_number, $client_ip, 'error', '手机号码格式不正确');
        echo json_encode(['success' => false, 'message' => '请输入正确的手机号码格式']);
        exit();
    }
} else {
    // 卡密登录模式
    if (empty($key)) {
        logApiCall('', $client_ip, 'error', '卡密不能为空');
        echo json_encode(['success' => false, 'message' => '卡密不能为空']);
        exit();
    }
}

// 限制卡密长度，避免缓冲区溢出攻击
if (strlen($key) > 255) {
    logApiCall(substr($key, 0, 20) . '...', $client_ip, 'error', '卡密格式无效');
    echo json_encode(['success' => false, 'message' => '卡密格式无效']);
    exit();
}

// 【新增】检查数据库连接
if (!checkDatabaseConnection()) {
    logApiCall($key, $client_ip, 'error', '数据库连接失败');
    echo json_encode(['success' => false, 'message' => '系统维护中，请稍后重试']);
    exit();
}

try {
    // 根据登录类型查询卡密信息
    if ($login_type === 'phone') {
        // 手机号码登录：通过手机号码查询卡密
        $stmt = $pdo->prepare("SELECT * FROM license_keys WHERE phone_number = ? AND status = 'active'");
        $stmt->execute([$phone_number]);
        $license = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$license) {
            logApiCall($phone_number, $client_ip, 'error', '手机号码未绑定有效卡密');
            echo json_encode([
                'success' => false,
                'message' => '该手机号码未绑定有效卡密，请使用卡密登录',
                'error_code' => 'PHONE_NOT_BOUND'
            ]);
            exit();
        }

        // 记录手机号码登录日志
        try {
            $log_stmt = $pdo->prepare("INSERT INTO phone_login_logs (phone_number, license_key_id, login_time, ip_address, user_agent, login_result) VALUES (?, ?, NOW(), ?, ?, 'success')");
            $log_stmt->execute([$phone_number, $license['id'], $client_ip, $_SERVER['HTTP_USER_AGENT'] ?? '']);
        } catch (Exception $e) {
            error_log("记录手机号码登录日志失败: " . $e->getMessage());
        }

    } else {
        // 卡密登录：通过卡密查询
        $stmt = $pdo->prepare("SELECT * FROM license_keys WHERE key_value = ?");
        $stmt->execute([$key]);
        $license = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$license) {
            logApiCall($key, $client_ip, 'error', '卡密不存在');
            echo json_encode([
                'success' => false,
                'message' => '卡密不存在',
                'error_code' => 'KEY_DELETED'
            ]);
            exit();
        }

        // 如果是绑定手机号码的请求
        if ($bind_phone && !empty($phone_number)) {
            // 验证手机号码格式
            if (!preg_match('/^1[3-9]\d{9}$/', $phone_number)) {
                logApiCall($key, $client_ip, 'error', '手机号码格式不正确');
                echo json_encode(['success' => false, 'message' => '请输入正确的手机号码格式']);
                exit();
            }

            // 检查手机号码是否已被其他卡密绑定
            $phone_check_stmt = $pdo->prepare("SELECT id, key_value FROM license_keys WHERE phone_number = ? AND status = 'active' AND id != ?");
            $phone_check_stmt->execute([$phone_number, $license['id']]);
            $existing_phone = $phone_check_stmt->fetch(PDO::FETCH_ASSOC);

            if ($existing_phone) {
                logApiCall($key, $client_ip, 'error', '手机号码已被其他卡密绑定');
                echo json_encode([
                    'success' => false,
                    'message' => '该手机号码已被其他卡密绑定，一个手机号码只能绑定一个卡密',
                    'error_code' => 'PHONE_ALREADY_BOUND'
                ]);
                exit();
            }

            // 绑定手机号码到卡密
            $bind_stmt = $pdo->prepare("UPDATE license_keys SET phone_number = ? WHERE id = ?");
            if ($bind_stmt->execute([$phone_number, $license['id']])) {
                $license['phone_number'] = $phone_number; // 更新本地数据
                error_log("卡密 {$key} 成功绑定手机号码 {$masked_phone}");
            } else {
                logApiCall($key, $client_ip, 'error', '手机号码绑定失败');
                echo json_encode(['success' => false, 'message' => '手机号码绑定失败，请重试']);
                exit();
            }
        }
    }

    // 【修复】检查卡密状态
    if ($license['status'] !== 'active') {
        $error_code = 'KEY_DISABLED';
        $message = '卡密状态异常: ' . $license['status'];
        
        // 根据具体状态设置错误代码
        switch ($license['status']) {
            case 'disabled':
                $error_code = 'KEY_DISABLED';
                $message = '您的卡密已经被禁用，如有疑问请联系代理商';
                break;
            case 'expired':
                $error_code = 'KEY_EXPIRED';
                $message = '您的卡密已经过期，如需继续使用请联系代理商';
                break;
            case 'suspended':
                $error_code = 'KEY_DISABLED';
                $message = '您的卡密已被暂停使用，如有疑问请联系代理商';
                break;
            default:
                $error_code = 'KEY_DISABLED';
                $message = '您的卡密已经被禁用，如有疑问请联系代理商';
                break;
        }
        
        logApiCall($key, $client_ip, 'error', $message);
        echo json_encode([
            'success' => false, 
            'message' => $message,
            'error_code' => $error_code
        ]);
        exit();
    }

    // 【修复】检查过期时间 - 兼容多种日期字段
    $expiry_field = null;
    $expiry_date = null;
    
    if (!empty($license['expiry_date'])) {
        $expiry_field = 'expiry_date';
        $expiry_date = new DateTime($license['expiry_date']);
    } elseif (!empty($license['expires_at'])) {
        $expiry_field = 'expires_at';
        $expiry_date = new DateTime($license['expires_at']);
    }
    
    if ($expiry_date) {
        $now = new DateTime();
        if ($now > $expiry_date) {
            // 【修复】自动将过期的卡密状态设为 expired，使用参数化查询
            $update_stmt = $pdo->prepare("UPDATE license_keys SET status = 'expired' WHERE id = ?");
            $update_stmt->execute([$license['id']]);
            
            $message = '您的卡密已经过期，如需继续使用请联系代理商';
            logApiCall($key, $client_ip, 'error', $message);
            echo json_encode([
                'success' => false, 
                'message' => $message,
                'error_code' => 'KEY_EXPIRED'
            ]);
            exit();
        }
    }

    // 获取所有店铺信息（包括主店铺和额外店铺）
    $stores = [];
    
    // 添加主店铺
    if (!empty($license['store_name']) || !empty($license['wechat_store_id'])) {
        $stores[] = [
            'store_name' => $license['store_name'],
            'wechat_store_id' => $license['wechat_store_id']
        ];
    }
    
    // 如果是多店铺卡密，获取额外的店铺信息
    if ($license['is_multi_store']) {
        try {
            $stores_stmt = $pdo->prepare("SELECT store_name, wechat_store_id FROM license_key_stores WHERE license_key_id = ? ORDER BY id ASC");
            $stores_stmt->execute([$license['id']]);
            $additional_stores = $stores_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($additional_stores)) {
                $stores = array_merge($stores, $additional_stores);
            }
        } catch (Exception $e) {
            error_log("获取额外店铺信息失败: " . $e->getMessage());
        }
    }

    // 【修复】添加店铺信息验证
    if (!empty($shop_name) || !empty($shop_id)) {
        $store_match = false;
        
        foreach ($stores as $store) {
            $db_shop_name = $store['store_name'];
            $db_shop_id = $store['wechat_store_id'];
            
            // 检查店铺名称或ID是否匹配任一店铺
            if ((!empty($db_shop_name) && !empty($shop_name) && trim($db_shop_name) === trim($shop_name)) ||
                (!empty($db_shop_id) && !empty($shop_id) && trim($db_shop_id) === trim($shop_id))) {
                $store_match = true;
                break;
            }
        }
        
        if (!$store_match) {
            logApiCall($key, $client_ip, 'error', '店铺信息不匹配');
            echo json_encode([
                'success' => false,
                'message' => '卡密与店铺信息不一致，无法登录'
            ]);
            exit;
        }
        
        logApiCall($key, $client_ip, 'info', "店铺信息验证通过: 提交({$shop_name}, {$shop_id})");
    }

    // 【修复】验证通过, 检查卡密功能权限
    $has_customer_service = $license['has_customer_service'] ?? 0;
    $has_product_listing = $license['has_product_listing'] ?? 0;

    if (!$has_customer_service && !$has_product_listing) {
        logApiCall($key, $client_ip, 'error', '此卡密没有任何功能权限');
        echo json_encode(['success' => false, 'message' => '此卡密没有任何功能权限']);
        exit();
    }

    // 【修复】根据卡密功能获取对应的脚本代码
    $script_conditions = [];
    $script_params = [];

    // 如果卡密有微信小店功能，获取微信小店脚本
    if ($has_customer_service) {
        $script_conditions[] = "has_wechat_store = 1";
    }

    // 如果卡密有抖店功能，获取抖店脚本
    if ($has_product_listing) {
        $script_conditions[] = "has_douyin_store = 1";
    }

    $script_sql = "
        SELECT script_code, content, name, version
        FROM scripts
        WHERE status = 'active' AND (" . implode(' OR ', $script_conditions) . ")
        ORDER BY updated_at DESC
        LIMIT 1
    ";

    $script_stmt = $pdo->prepare($script_sql);
    $script_stmt->execute($script_params);
    $script_row = $script_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$script_row) {
        logApiCall($key, $client_ip, 'error', '找不到对应功能的脚本');
        echo json_encode(['success' => false, 'message' => '找不到对应功能的脚本']);
        exit();
    }

    // 优先使用script_code，如果没有则使用content
    $script_code = $script_row['script_code'] ?? $script_row['content'] ?? '';

    if (empty($script_code)) {
        logApiCall($key, $client_ip, 'error', '脚本代码为空');
        echo json_encode(['success' => false, 'message' => '脚本代码为空']);
        exit();
    }



    // 【新增】如果是状态检查请求，只返回状态信息，不返回脚本代码
    if ($check_status === '1') {
        // 确定功能类型
        $has_customer_service = $license['has_customer_service'] ?? 1;
        $has_product_listing = $license['has_product_listing'] ?? 0;
        
        $function_type = '';
        if ($has_customer_service && $has_product_listing) {
            $function_type = 'full_features';
        } elseif ($has_product_listing && !$has_customer_service) {
            $function_type = 'product_listing';
        } elseif ($has_customer_service && !$has_product_listing) {
            $function_type = 'customer_service';
        } else {
            $function_type = 'no_features';
        }
        
        logApiCall($key, $client_ip, 'success', "状态检查成功 - 功能类型: {$function_type}");
        
        echo json_encode([
            'success' => true,
            'message' => '卡密状态正常',
            'function_type' => $function_type,
            'has_customer_service' => (bool)$has_customer_service,
            'has_product_listing' => (bool)$has_product_listing,
            'expiry_date' => $license['expiry_date'] ?? $license['expires_at'] ?? null,
            'status' => $license['status'],
            'stores' => $stores,
            'is_multi_store' => (bool)$license['is_multi_store']
        ]);
        exit();
    }

    // 【修复】更新IP地址和最后心跳时间 - 使用参数化查询
    try {
        $update_ip_stmt = $pdo->prepare("UPDATE license_keys SET last_used_ip = ?, last_heartbeat = NOW() WHERE id = ?");
        $update_ip_stmt->execute([$client_ip, $license['id']]);
    } catch (Exception $e) {
        // 如果字段不存在，尝试使用其他字段
        try {
            $update_ip_stmt = $pdo->prepare("UPDATE license_keys SET last_used_ip = ? WHERE id = ?");
            $update_ip_stmt->execute([$client_ip, $license['id']]);
        } catch (Exception $e2) {
            // 记录错误但不影响主流程
            error_log("更新IP地址失败: " . $e2->getMessage());
        }
    }

    // 【修复】确定功能类型和提示信息
    $has_customer_service = $license['has_customer_service'] ?? 1;
    $has_product_listing = $license['has_product_listing'] ?? 0;
    
    $function_type = '';
    $success_message = '';
    
    if ($has_customer_service && $has_product_listing) {
        $function_type = 'full_features';
        $success_message = '卡密验证通过，恭喜您已成功开通"<span style="color: #ff0000; font-weight: bold;">小梅花AI客服-微信小店+抖店</span>"，感谢您选择小梅花AI智能客服系统！';
    } elseif ($has_product_listing && !$has_customer_service) {
        $function_type = 'product_listing';
        $success_message = '卡密验证通过，恭喜您已成功开通"<span style="color: #ff0000; font-weight: bold;">小梅花AI客服-抖店</span>"，感谢您选择小梅花AI智能客服系统！';
    } elseif ($has_customer_service && !$has_product_listing) {
        $function_type = 'customer_service';
        $success_message = '卡密验证通过，恭喜您已成功开通"<span style="color: #ff0000; font-weight: bold;">小梅花AI客服-微信小店</span>"，感谢您选择小梅花AI智能客服系统！';
    } else {
        $function_type = 'no_features';
        $success_message = '卡密验证通过，但此卡密未开通任何功能，请联系管理员。';
    }

    logApiCall($key, $client_ip, 'success', "验证成功 - 功能类型: {$function_type}");
    
    // 【修复】从脚本代码中提取@match规则
    $match_urls = [];
    
    // 匹配所有@match行
    if (preg_match_all('/@match\s+(.+)/i', $script_code, $matches)) {
        foreach ($matches[1] as $match) {
            $url = trim($match);
            if (!empty($url)) {
                $match_urls[] = $url;
            }
        }
    }
    
    // 【重要】如果没有找到@match规则，设置为空数组而不是通配符
    // 这样可以让APP端决定如何处理
    if (empty($match_urls)) {
        error_log("脚本中未找到@match规则，将由APP端处理向后兼容");
    }
    
    error_log("提取到的@match规则: " . json_encode($match_urls));
    
    // 【修复】构建返回数据
    $response_data = [
        'success' => true,
        'script' => $script_code,
        'match_urls' => $match_urls,  // 新增：URL匹配规则
        'expiry_date' => $license['expiry_date'] ?? $license['expires_at'] ?? null,
        'type' => $license['type'] ?? 'day',
        'store_name' => $license['store_name'] ?? '',
        'wechat_store_id' => $license['wechat_store_id'] ?? '',
        'stores' => $stores,
        'is_multi_store' => (bool)$license['is_multi_store'],
        'function_type' => $function_type,
        'message' => $success_message,
        'has_customer_service' => (bool)$has_customer_service,
        'has_product_listing' => (bool)$has_product_listing,
        'phone_number' => $license['phone_number'] ?? null,  // 新增：绑定的手机号码
        'phone_bound' => !empty($license['phone_number'])    // 新增：是否已绑定手机号码
    ];

    // 如果是手机号码登录，添加卡密信息
    if ($login_type === 'phone') {
        $response_data['license_key'] = $license['key_value'];
    }


    
    // 返回成功响应
    echo json_encode($response_data);
    
} catch (PDOException $e) {
    // 【修复】数据库错误的详细处理
    $error_message = "数据库错误: " . $e->getMessage();
    error_log($error_message);
    logApiCall($key, $client_ip, 'error', $error_message);
    
    // 根据错误类型返回不同的用户友好消息
    if (strpos($e->getMessage(), 'Connection refused') !== false) {
        echo json_encode(['success' => false, 'message' => '数据库连接失败，请稍后重试']);
    } elseif (strpos($e->getMessage(), "Table") !== false && strpos($e->getMessage(), "doesn't exist") !== false) {
        echo json_encode(['success' => false, 'message' => '系统配置错误，请联系管理员']);
    } elseif (strpos($e->getMessage(), "Unknown column") !== false) {
        echo json_encode(['success' => false, 'message' => '系统版本不兼容，请联系管理员']);
    } else {
        echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    }
} catch (Exception $e) {
    // 【修复】其他异常的详细处理
    $error_message = "系统异常: " . $e->getMessage();
    error_log($error_message);
    logApiCall($key, $client_ip, 'error', $error_message);
    echo json_encode(['success' => false, 'message' => '系统异常，请稍后重试']);
}
?> 