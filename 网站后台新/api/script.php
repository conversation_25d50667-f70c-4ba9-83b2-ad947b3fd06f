<?php
/**
 * 独立脚本API - 专门用于APP获取脚本内容和@match规则
 * 解决脚本更新不及时的问题
 * 
 * 功能：
 * 1. 根据卡密获取对应的脚本内容
 * 2. 提取并返回@match规则
 * 3. 提供版本控制和更新检测
 * 4. 支持强制刷新和缓存控制
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入数据库配置
require_once '../config/database.php';

// 日志记录函数
function logScriptApi($key, $ip, $action, $message) {
    $logFile = '../logs/script_api.log';
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[{$timestamp}] IP:{$ip} KEY:{$key} ACTION:{$action} - {$message}\n";
    
    // 确保日志目录存在
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

try {
    // 获取客户端IP
    $client_ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['HTTP_X_REAL_IP'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    // 获取请求参数
    $key = $_POST['key'] ?? $_GET['key'] ?? '';
    $force_refresh = $_POST['force_refresh'] ?? $_GET['force_refresh'] ?? '0';
    $version_check = $_POST['version_check'] ?? $_GET['version_check'] ?? '0';
    
    if (empty($key)) {
        logScriptApi('', $client_ip, 'error', '缺少卡密参数');
        echo json_encode([
            'success' => false,
            'message' => '缺少卡密参数',
            'error_code' => 'MISSING_KEY'
        ]);
        exit;
    }
    
    logScriptApi($key, $client_ip, 'request', "获取脚本请求 - 强制刷新:{$force_refresh}, 版本检查:{$version_check}");
    
    // 连接数据库
    $pdo = new PDO($dsn, $username, $password, $options);
    
    // 验证卡密有效性
    $license_stmt = $pdo->prepare("
        SELECT l.*, s.script_code, s.content, s.updated_at as script_updated_at
        FROM license_keys l 
        LEFT JOIN scripts s ON l.script_id = s.id 
        WHERE l.license_key = ? AND l.status = 'active'
    ");
    $license_stmt->execute([$key]);
    $license = $license_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$license) {
        logScriptApi($key, $client_ip, 'error', '卡密无效或已过期');
        echo json_encode([
            'success' => false,
            'message' => '卡密无效或已过期',
            'error_code' => 'INVALID_KEY'
        ]);
        exit;
    }
    
    // 检查卡密是否过期
    $expiry_date = $license['expiry_date'] ?? $license['expires_at'] ?? null;
    if ($expiry_date && strtotime($expiry_date) < time()) {
        logScriptApi($key, $client_ip, 'error', '卡密已过期');
        echo json_encode([
            'success' => false,
            'message' => '卡密已过期',
            'error_code' => 'KEY_EXPIRED'
        ]);
        exit;
    }
    
    // 获取脚本内容
    $script_code = $license['script_code'] ?? $license['content'] ?? '';
    
    if (empty($script_code)) {
        logScriptApi($key, $client_ip, 'error', '未找到关联的脚本内容');
        echo json_encode([
            'success' => false,
            'message' => '未找到关联的脚本内容',
            'error_code' => 'NO_SCRIPT'
        ]);
        exit;
    }
    
    // 提取@match规则
    $match_urls = [];
    if (preg_match_all('/@match\s+(.+)/i', $script_code, $matches)) {
        foreach ($matches[1] as $match) {
            $url = trim($match);
            if (!empty($url)) {
                $match_urls[] = $url;
            }
        }
    }
    
    // 生成脚本版本号（基于内容和更新时间）
    $script_updated_at = $license['script_updated_at'] ?? date('Y-m-d H:i:s');
    $script_version = md5($script_code . $script_updated_at);
    
    // 如果是版本检查请求
    if ($version_check === '1') {
        $client_version = $_POST['client_version'] ?? $_GET['client_version'] ?? '';
        
        $has_update = ($client_version !== $script_version);
        
        logScriptApi($key, $client_ip, 'version_check', "版本检查 - 客户端:{$client_version}, 服务端:{$script_version}, 有更新:{$has_update}");
        
        echo json_encode([
            'success' => true,
            'has_update' => $has_update,
            'server_version' => $script_version,
            'client_version' => $client_version,
            'last_updated' => $script_updated_at
        ]);
        exit;
    }
    
    // 更新最后使用时间
    try {
        $update_stmt = $pdo->prepare("UPDATE license_keys SET last_used_ip = ?, last_heartbeat = NOW() WHERE license_key = ?");
        $update_stmt->execute([$client_ip, $key]);
    } catch (Exception $e) {
        // 记录错误但不影响主流程
        logScriptApi($key, $client_ip, 'warning', '更新使用记录失败: ' . $e->getMessage());
    }
    
    // 构建响应数据
    $response_data = [
        'success' => true,
        'script' => $script_code,
        'match_urls' => $match_urls,
        'script_version' => $script_version,
        'last_updated' => $script_updated_at,
        'match_count' => count($match_urls),
        'force_refresh' => ($force_refresh === '1'),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    logScriptApi($key, $client_ip, 'success', "脚本获取成功 - 版本:{$script_version}, @match规则:" . count($match_urls) . "个");
    
    echo json_encode($response_data);
    
} catch (PDOException $e) {
    logScriptApi($key ?? '', $client_ip, 'error', '数据库错误: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '数据库连接失败，请稍后重试',
        'error_code' => 'DATABASE_ERROR'
    ]);
} catch (Exception $e) {
    logScriptApi($key ?? '', $client_ip, 'error', '系统错误: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误，请稍后重试',
        'error_code' => 'SYSTEM_ERROR'
    ]);
}
?>
