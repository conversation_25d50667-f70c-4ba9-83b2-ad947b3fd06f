<?php
/**
 * API网关
 * 处理所有API请求，实现路由和安全验证
 * 
 * @version 1.0.0
 */

// 定义API访问常量
define('API_ACCESS', true);

// 错误处理
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/logs/api_errors.log');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 加载必要的文件
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/api_base.php';
require_once __DIR__ . '/env_manager.php';

/**
 * API网关类
 */
class ApiGateway extends ApiBase {
    private $routes = [];
    private $dynamicEndpoints = [];

    /**
     * 构造函数
     */
    public function __construct() {
        parent::__construct();
        
        // 初始化路由
        $this->initRoutes();
        
        // 加载动态端点
        $this->loadDynamicEndpoints();
    }

    /**
     * 初始化路由
     */
    private function initRoutes() {
        // 基础API路由
        $this->routes = [
            'verify' => [
                'handler' => 'handleVerify',
                'requireAuth' => false
            ],
            'status' => [
                'handler' => 'handleStatus',
                'requireAuth' => true
            ],
            'heartbeat' => [
                'handler' => 'handleHeartbeat',
                'requireAuth' => true
            ],
            'exec' => [
                'handler' => 'handleExec',
                'requireAuth' => true
            ]
        ];
    }

    /**
     * 加载动态端点
     */
    private function loadDynamicEndpoints() {
        try {
            $stmt = $this->db->query("SELECT endpoint_id, endpoint_name, handler_function, require_auth FROM dynamic_endpoints WHERE is_active = 1");
            $endpoints = $stmt->fetchAll();
            
            foreach ($endpoints as $endpoint) {
                $this->dynamicEndpoints[$endpoint['endpoint_name']] = [
                    'handler' => $endpoint['handler_function'],
                    'requireAuth' => (bool)$endpoint['require_auth'],
                    'id' => $endpoint['endpoint_id']
                ];
            }
        } catch (PDOException $e) {
            // 表可能不存在，忽略错误
        }
    }

    /**
     * 处理请求
     */
    public function handleRequest() {
        // 解析请求路径
        $path = $_SERVER['PATH_INFO'] ?? '';
        $path = trim($path, '/');
        
        // 分割路径获取端点
        $segments = explode('/', $path);
        $endpoint = $segments[0] ?? '';
        
        // 检查端点是否存在
        if (empty($endpoint)) {
            return $this->respondError('Invalid endpoint', 404);
        }
        
        // 检查是否是标准路由
        if (isset($this->routes[$endpoint])) {
            $route = $this->routes[$endpoint];
            $handler = $route['handler'];
            $requireAuth = $route['requireAuth'];
            
            // 验证身份
            if ($requireAuth && !$this->authenticate()) {
                return $this->respondError('Unauthorized', 401);
            }
            
            // 调用处理函数
            return $this->$handler();
        }
        
        // 检查是否是动态端点
        if (isset($this->dynamicEndpoints[$endpoint])) {
            $route = $this->dynamicEndpoints[$endpoint];
            $handler = $route['handler'];
            $requireAuth = $route['requireAuth'];
            
            // 验证身份
            if ($requireAuth && !$this->authenticate()) {
                return $this->respondError('Unauthorized', 401);
            }
            
            // 调用动态处理函数
            return $this->handleDynamicEndpoint($handler, $route['id']);
        }
        
        // 端点不存在
        return $this->respondError('Endpoint not found', 404);
    }

    /**
     * 验证身份
     */
    private function authenticate() {
        // 获取Authorization头
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        if (empty($authHeader)) {
            return false;
        }
        
        // 解析Bearer令牌
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
            $this->token = $token;
            
            // 验证令牌
            return $this->verifyToken($token);
        }
        
        return false;
    }

    /**
     * 处理验证请求
     */
    private function handleVerify() {
        // 检查请求方法
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }
        
        // 获取请求数据
        $key = $this->request['key'] ?? '';
        $timestamp = $this->request['timestamp'] ?? '';
        $shopInfo = $this->request['shop_info'] ?? [];
        $clientInfo = $this->request['client_info'] ?? [];
        
        // 验证必要参数
        if (empty($key) || empty($timestamp)) {
            return $this->respondError('Missing required parameters', 400);
        }
        
        // 验证时间戳
        if (!$this->verifyTimestamp($timestamp)) {
            return $this->respondError('Invalid timestamp', 400, 'INVALID_TIMESTAMP');
        }
        
        // 验证卡密
        try {
            $stmt = $this->db->prepare("
                SELECT lk.*, s.code as script_code 
                FROM license_keys lk
                LEFT JOIN scripts s ON lk.script_id = s.id
                WHERE lk.key_value = ? AND lk.status = 'active' AND lk.expiry_date > NOW()
            ");
            $stmt->execute([$key]);
            $keyData = $stmt->fetch();
            
            if (!$keyData) {
                // 检查是否过期
                $stmt = $this->db->prepare("
                    SELECT * FROM license_keys 
                    WHERE key_value = ? AND status = 'active' AND expiry_date <= NOW()
                ");
                $stmt->execute([$key]);
                if ($stmt->fetch()) {
                    return $this->respondError('License key has expired', 403, 'KEY_EXPIRED');
                }
                
                // 检查是否被禁用
                $stmt = $this->db->prepare("
                    SELECT * FROM license_keys 
                    WHERE key_value = ? AND status = 'banned'
                ");
                $stmt->execute([$key]);
                if ($stmt->fetch()) {
                    return $this->respondError('License key has been banned', 403, 'KEY_DISABLED');
                }
                
                return $this->respondError('Invalid license key', 403, 'INVALID_KEY');
            }
            
            // 更新卡密使用信息
            $stmt = $this->db->prepare("
                UPDATE license_keys 
                SET last_used_at = NOW(), 
                    use_count = use_count + 1,
                    last_ip = ?,
                    last_user_agent = ?
                WHERE id = ?
            ");
            $stmt->execute([
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $keyData['id']
            ]);
            
            // 记录卡密使用日志
            $stmt = $this->db->prepare("
                INSERT INTO key_usage_logs 
                (key_id, ip_address, user_agent, shop_name, shop_url, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $keyData['id'],
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $shopInfo['name'] ?? '',
                $shopInfo['url'] ?? ''
            ]);
            
            // 生成安全令牌
            $securityToken = bin2hex(random_bytes(16));
            
            // 生成动态端点
            $dynamicEndpoint = $this->generateDynamicEndpoint($key, $timestamp);
            
            // 保存动态端点
            $stmt = $this->db->prepare("
                INSERT INTO dynamic_endpoints 
                (endpoint_name, key_id, handler_function, require_auth, expires_at, created_at) 
                VALUES (?, ?, 'handleExec', 1, DATE_ADD(NOW(), INTERVAL 30 MINUTE), NOW())
            ");
            $stmt->execute([$dynamicEndpoint, $keyData['id']]);
            
            // 准备响应数据
            $responseData = [
                'success' => true,
                'message' => '验证成功',
                'security_token' => $securityToken,
                'endpoint' => $dynamicEndpoint,
                'function_type' => $keyData['type'],
                'has_customer_service' => (bool)$keyData['has_customer_service'],
                'has_product_listing' => (bool)$keyData['has_product_listing'],
                'script' => $keyData['script_code'] ?? ''
            ];
            
            return $this->respondSuccess($responseData);
            
        } catch (PDOException $e) {
            error_log("验证卡密失败: " . $e->getMessage());
            return $this->respondError('Server error', 500);
        }
    }

    /**
     * 处理状态请求
     */
    private function handleStatus() {
        // 检查请求方法
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }
        
        // 获取请求数据
        $key = $this->request['key'] ?? '';
        $checkStatus = $this->request['check_status'] ?? 0;
        
        // 验证必要参数
        if (empty($key)) {
            return $this->respondError('Missing required parameters', 400);
        }
        
        // 验证卡密
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM license_keys 
                WHERE key_value = ?
            ");
            $stmt->execute([$key]);
            $keyData = $stmt->fetch();
            
            if (!$keyData) {
                return $this->respondError('License key not found', 404, 'KEY_DELETED');
            }
            
            // 检查卡密状态
            if ($keyData['status'] !== 'active') {
                return $this->respondError('License key is not active', 403, 'KEY_DISABLED');
            }
            
            // 检查是否过期
            if (strtotime($keyData['expiry_date']) <= time()) {
                return $this->respondError('License key has expired', 403, 'KEY_EXPIRED');
            }
            
            // 生成新的安全令牌
            $securityToken = bin2hex(random_bytes(16));
            
            // 准备响应数据
            $responseData = [
                'success' => true,
                'message' => '卡密状态正常',
                'security_token' => $securityToken,
                'function_type' => $keyData['type'],
                'has_customer_service' => (bool)$keyData['has_customer_service'],
                'has_product_listing' => (bool)$keyData['has_product_listing'],
                'expiry_date' => $keyData['expiry_date']
            ];
            
            return $this->respondSuccess($responseData);
            
        } catch (PDOException $e) {
            error_log("检查卡密状态失败: " . $e->getMessage());
            return $this->respondError('Server error', 500);
        }
    }

    /**
     * 处理心跳请求
     */
    private function handleHeartbeat() {
        // 检查请求方法
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }
        
        // 获取请求数据
        $key = $this->request['key'] ?? '';
        $timestamp = $this->request['timestamp'] ?? '';
        $clientInfo = $this->request['client_info'] ?? [];
        
        // 验证必要参数
        if (empty($key) || empty($timestamp)) {
            return $this->respondError('Missing required parameters', 400);
        }
        
        // 验证卡密
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM license_keys 
                WHERE key_value = ? AND status = 'active' AND expiry_date > NOW()
            ");
            $stmt->execute([$key]);
            $keyData = $stmt->fetch();
            
            if (!$keyData) {
                return $this->respondError('Invalid license key', 403);
            }
            
            // 更新最后活动时间
            $stmt = $this->db->prepare("
                UPDATE license_keys 
                SET last_heartbeat_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$keyData['id']]);
            
            // 记录心跳日志
            $stmt = $this->db->prepare("
                INSERT INTO heartbeat_logs 
                (key_id, ip_address, user_agent, client_info, created_at) 
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $keyData['id'],
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                json_encode($clientInfo)
            ]);
            
            // 生成新的安全令牌
            $securityToken = bin2hex(random_bytes(16));
            
            // 准备响应数据
            $responseData = [
                'success' => true,
                'message' => '心跳成功',
                'security_token' => $securityToken
            ];
            
            return $this->respondSuccess($responseData);
            
        } catch (PDOException $e) {
            error_log("心跳请求失败: " . $e->getMessage());
            return $this->respondError('Server error', 500);
        }
    }

    /**
     * 处理脚本执行请求
     */
    private function handleExec() {
        // 检查请求方法
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }
        
        // 获取请求数据
        $function = $this->request['function'] ?? '';
        $params = $this->request['params'] ?? [];
        
        // 验证必要参数
        if (empty($function)) {
            return $this->respondError('Missing required parameters', 400);
        }
        
        // 获取卡密信息
        try {
            // 从令牌中获取用户ID
            $userId = $this->getUserIdFromToken();
            if (!$userId) {
                return $this->respondError('Invalid token', 401);
            }
            
            // 获取卡密信息 - 移除script_id依赖
            $stmt = $this->db->prepare("
                SELECT lk.*
                FROM license_keys lk
                WHERE lk.user_id = ? AND lk.status = 'active' AND lk.expiry_date > NOW()
            ");
            $stmt->execute([$userId]);
            $keyData = $stmt->fetch();
            
            if (!$keyData) {
                return $this->respondError('Invalid license key', 403);
            }
            
            // 检查卡密功能权限
            $has_customer_service = $keyData['has_customer_service'] ?? 0;
            $has_product_listing = $keyData['has_product_listing'] ?? 0;

            if (!$has_customer_service && !$has_product_listing) {
                return $this->respondError('No function permissions', 403);
            }
            
            // 执行脚本函数
            $result = $this->executeScriptFunction($keyData['script_code'], $function, $params);
            
            // 准备响应数据
            $responseData = [
                'success' => true,
                'message' => '执行成功',
                'result' => $result
            ];
            
            return $this->respondSuccess($responseData);
            
        } catch (Exception $e) {
            error_log("执行脚本失败: " . $e->getMessage());
            return $this->respondError('Script execution failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 处理动态端点请求
     */
    private function handleDynamicEndpoint($handler, $endpointId) {
        // 检查处理函数是否存在
        if (!method_exists($this, $handler)) {
            return $this->respondError('Handler not found', 500);
        }
        
        // 更新端点使用记录
        try {
            $stmt = $this->db->prepare("
                UPDATE dynamic_endpoints 
                SET last_used_at = NOW(), 
                    use_count = use_count + 1
                WHERE endpoint_id = ?
            ");
            $stmt->execute([$endpointId]);
        } catch (PDOException $e) {
            // 忽略错误
        }
        
        // 调用处理函数
        return $this->$handler();
    }

    /**
     * 从令牌中获取用户ID
     */
    private function getUserIdFromToken() {
        if (empty($this->token)) {
            return null;
        }
        
        try {
            $stmt = $this->db->prepare("SELECT user_id FROM api_tokens WHERE token = ? AND is_active = 1 AND expires_at > NOW()");
            $stmt->execute([$this->token]);
            $tokenData = $stmt->fetch();
            
            return $tokenData ? $tokenData['user_id'] : null;
        } catch (PDOException $e) {
            return null;
        }
    }

    /**
     * 执行脚本函数
     */
    private function executeScriptFunction($scriptCode, $function, $params) {
        // 创建一个安全的执行环境
        $sandbox = function ($scriptCode, $function, $params) {
            // 创建函数名
            $functionName = "script_function_" . md5($function . time());
            
            // 创建函数代码
            $functionCode = "
                function {$functionName}(\$params) {
                    {$scriptCode}
                    
                    if (function_exists('{$function}')) {
                        return {$function}(\$params);
                    } else {
                        throw new Exception('Function {$function} not found in script');
                    }
                }
            ";
            
            // 执行函数代码
            eval($functionCode);
            
            // 调用函数
            return call_user_func($functionName, $params);
        };
        
        // 执行沙盒
        return $sandbox($scriptCode, $function, $params);
    }

    /**
     * 生成动态端点
     */
    private function generateDynamicEndpoint($key, $timestamp) {
        $salt = $this->config->get('api_secret_key');
        $base = "{$key}{$timestamp}{$salt}";
        $hash = hash('sha256', $base);
        return substr($hash, 0, 12);
    }
}

// 创建API网关实例并处理请求
$gateway = new ApiGateway();
$gateway->handleRequest(); 