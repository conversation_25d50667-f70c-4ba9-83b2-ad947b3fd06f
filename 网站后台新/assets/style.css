/* ========== 重置和基础样式 ========== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    background-image: none !important; /* 确保没有背景图片 */
}

*::before, *::after {
    background-image: none !important; /* 确保伪元素也没有背景图片 */
}

/* 强制覆盖任何可能的body伪元素 */
body::before, body::after {
    display: none !important;
    content: none !important;
    background: none !important;
    transform: none !important;
    animation: none !important;
}

html, body {
    height: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; /* 强制只使用渐变背景 */
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: cover;
    overflow-x: hidden;
    /* 防止滚动边界出现白色 */
    overscroll-behavior: none !important;
    -webkit-overscroll-behavior: none !important;
}

/* 为整个页面添加背景保护层 */
html::after {
    content: '';
    position: fixed;
    top: -200px;
    left: -200px;
    right: -200px;
    bottom: -200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: -10;
    pointer-events: none;
}

/* ========== 粒子背景 - 已禁用以提升性能 ========== */
.particles {
    display: none !important;
}

.particle {
    display: none !important;
}

/* ========== 后台布局容器 ========== */
/* 
.admin-layout {
    display: flex;
    height: 100vh;
    width: 100%;
    position: relative;
    z-index: 10;
}
*/

/* ========== 侧边栏样式 ========== */
.sidebar {
    position: fixed !important;
    top: 70px !important;
    left: 0 !important;
    width: 260px !important;
    height: calc(100vh - 70px) !important;
    background: rgba(255, 255, 255, 0.15);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    display: flex !important;
    flex-direction: column;
    z-index: 1000 !important;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
    transform: translateX(0) !important;
}

/* 侧边栏头部 */
.sidebar-header {
    flex-shrink: 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-decoration: none;
    padding: 25px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
}

.logo i {
    font-size: 24px;
    color: #ff6b9d;
    flex-shrink: 0;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    box-shadow: 0 6px 20px rgba(255, 107, 157, 0.3);
    margin-right: 5px;
}

/* 管理员信息区域 */
.admin-info-sidebar {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-dropdown {
    position: relative;
}

.admin-toggle {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    cursor: pointer;
    font-size: 14px;
}

.admin-role {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-left: auto;
    margin-right: 8px;
}

.dropdown-arrow {
    /* 移除动画效果 */
}

.admin-dropdown.active .dropdown-arrow {
    transform: rotate(180deg);
}

.admin-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    z-index: 1001;
    margin-top: 5px;
}

.admin-dropdown.active .admin-dropdown-menu {
    opacity: 1;
    visibility: visible;
}

.admin-dropdown-menu .dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-dropdown-menu .dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 5px 0;
}

/* 侧边栏导航 */
.sidebar-nav {
    flex: 1;
    padding: 30px 0 20px 0;
    overflow-y: auto;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-link {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* 左对齐，但内容居中 */
    gap: 15px;
    padding: 14px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 0 25px 25px 0;
    margin-right: 15px;
    position: relative;
    font-size: 14px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center; /* 文字居中 */
}

.nav-link.active {
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(196, 69, 105, 0.9));
    color: white;
    box-shadow: 0 4px 20px rgba(255, 107, 157, 0.5);
    border: 1px solid rgba(255, 107, 157, 0.3);
}

.nav-link i {
    font-size: 16px;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

/* 确保菜单文字和图标整体居中 */
.nav-link .nav-text {
    flex: 1;
    text-align: center;
    margin-left: -35px; /* 补偿图标宽度，使文字视觉居中 */
}

/* ========== 二级菜单样式 - 向下垂直展开 ========== */
.nav-item.has-submenu {
    display: block !important;
    width: 100% !important;
    position: relative !important;
}

.nav-item.has-submenu .nav-link {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
}

.submenu-arrow {
    font-size: 12px !important;
    margin-left: auto !important;
}

.nav-item.has-submenu.active .submenu-arrow {
    transform: rotate(180deg) !important;
}

/* 二级菜单容器 - 向下展开 */
.submenu {
    display: none !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    background: rgba(0, 0, 0, 0.3) !important;
    border-radius: 0 !important;
    overflow: hidden !important;
    width: 100% !important;
    position: relative !important;
    box-sizing: border-box !important;
    margin-top: 0 !important;
    border-left: 2px solid rgba(255, 107, 157, 0.3) !important;
    margin-left: 15px !important;
    margin-right: 15px !important;
}

.nav-item.has-submenu.active .submenu {
    display: block !important;
}

/* 二级菜单项 - 垂直排列 */
.submenu-item {
    display: block !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.submenu-item:last-child {
    border-bottom: none !important;
}

/* 二级菜单链接 */
.submenu-link {
    display: block !important;
    padding: 10px 15px 10px 35px !important;
    color: rgba(255, 255, 255, 0.7) !important;
    text-decoration: none !important;
    font-size: 13px !important;
    font-weight: 400 !important;
    width: 100% !important;
    box-sizing: border-box !important;
    position: relative !important;
    border-radius: 0 !important;
}

.submenu-link:before {
    content: '' !important;
    position: absolute !important;
    left: 20px !important;
    top: 50% !important;
    width: 4px !important;
    height: 4px !important;
    background: rgba(255, 255, 255, 0.4) !important;
    border-radius: 50% !important;
    transform: translateY(-50%) !important;
}

.submenu-link.active {
    background: rgba(255, 107, 157, 0.15) !important;
    color: white !important;
    border-left: 3px solid #ff6b9d !important;
    padding-left: 32px !important;
}

.submenu-link.active:before {
    background: #ff6b9d !important;
    transform: translateY(-50%) scale(1.2) !important;
}

.submenu-link i {
    margin-right: 8px !important;
    font-size: 12px !important;
    width: 14px !important;
    text-align: center !important;
    opacity: 0.8 !important;
}

.submenu-text {
    font-weight: 400 !important;
    font-size: 13px !important;
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    flex-shrink: 0;
}

.version-info {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
}

/* ========== 主内容包装器 ========== */
.main-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    background: transparent;
    z-index: 10;
}

/* ========== 主头部 ========== */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    flex-shrink: 0;
    z-index: 1500;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-size: 18px;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
}

.header-title {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-title i {
    color: #ff6b9d;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-user {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 8px 15px;
    border-radius: 10px;
}

.user-info {
    text-align: right;
}

.user-name {
    color: white;
    font-weight: 500;
    display: block;
    font-size: 14px;
}

.user-role {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.user-avatar {
    color: rgba(255, 255, 255, 0.8);
    font-size: 24px;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(0, 0, 0, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    z-index: 1001;
    margin-top: 5px;
}

.user-dropdown.active {
    opacity: 1;
    visibility: visible;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    font-weight: 500;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
}

/* ========== 主内容区 ========== */
.main-content {
    margin-top: 70px;
    margin-left: 260px;
    padding: 30px;
    min-height: calc(100vh - 70px);
    overflow-y: auto;
    overflow-x: hidden;
    /* 防止滚动边界出现白色 */
    overscroll-behavior: contain !important;
    -webkit-overscroll-behavior: contain !important;
    background: transparent !important;
    box-sizing: border-box;
}

/* ========== 卡片样式 ========== */
.card {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card h2 {
    color: white;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

/* ========== 统计网格 ========== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.2);
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
}

.stat-item .value {
    font-size: 36px;
    font-weight: 700;
    color: white;
    margin-bottom: 8px;
}

.stat-item .label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

/* ========== 响应式设计 ========== */
/* 桌面端确保布局正确显示 */
@media (min-width: 769px) {
    .main-header {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 70px !important;
        z-index: 1500 !important;
    }
    
    .sidebar {
        position: fixed !important;
        top: 70px !important;
        left: 0 !important;
        width: 260px !important;
        height: calc(100vh - 70px) !important;
        transform: translateX(0) !important;
        z-index: 1000 !important;
    }
    
    .main-content {
        margin-top: 70px !important;
        margin-left: 260px !important;
        min-height: calc(100vh - 70px) !important;
    }
}

@media (max-width: 1200px) {
    .main-content {
        padding: 25px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .main-header {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        padding: 0 20px;
        height: 60px !important;
        z-index: 1500 !important;
    }
    
    .sidebar {
        position: fixed !important;
        top: 60px !important;
        left: 0 !important;
        width: 260px !important;
        height: calc(100vh - 60px) !important;
        transform: translateX(-100%) !important;
        z-index: 2000 !important;
    }
    
    .sidebar.active {
        transform: translateX(0) !important;
    }
    
    .header-title {
        font-size: 18px;
    }
    
    .user-info {
        display: none;
    }
    
    .main-content {
        margin-top: 60px !important;
        margin-left: 0 !important;
        padding: 20px 15px;
        min-height: calc(100vh - 60px) !important;
        /* 移动端防止滚动边界出现白色 */
        overscroll-behavior: contain !important;
        -webkit-overscroll-behavior: contain !important;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .card {
        padding: 20px;
        margin-bottom: 20px;
    }
}

@media (max-width: 480px) {
    .main-header {
        padding: 0 15px;
        height: 50px !important;
    }
    
    .sidebar {
        top: 50px !important;
        height: calc(100vh - 50px) !important;
    }
    
    .header-title {
        font-size: 16px;
    }
    
    .main-content {
        margin-top: 50px !important;
        padding: 15px 10px;
        min-height: calc(100vh - 50px) !important;
        /* 小屏幕移动端防止滚动边界出现白色 */
        overscroll-behavior: contain !important;
        -webkit-overscroll-behavior: contain !important;
    }
    
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 12px;
    }
}

/* ========== 表格样式 ========== */
.table-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 18px 15px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

table td {
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}

/* 移除表格行悬浮效果 */

/* ========== 表单样式 ========== */
.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: white;
    font-size: 14px;
}

input[type="text"],
input[type="password"],
input[type="number"],
input[type="email"],
select,
textarea {
    width: 100%;
    padding: 15px 20px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
    box-sizing: border-box;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.15);
}

input::placeholder, textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

textarea {
    min-height: 120px;
    font-family: 'Consolas', 'Monaco', monospace;
    resize: vertical;
}

/* ========== 按钮样式 - 简化版本 ========== */
.btn {
    padding: 12px 25px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-weight: 600;
    font-size: 14px;
    text-align: center;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    color: white;
}

.btn-primary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
    border-color: rgba(102, 126, 234, 0.4);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.3), rgba(238, 90, 82, 0.3));
    border-color: rgba(255, 107, 107, 0.4);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, rgba(81, 207, 102, 0.3), rgba(64, 192, 87, 0.3));
    border-color: rgba(81, 207, 102, 0.4);
    box-shadow: 0 4px 15px rgba(81, 207, 102, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

.btn-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 178, 7, 0.3));
    border-color: rgba(255, 193, 7, 0.4);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

/* 禁用状态 */
.btn:disabled,
.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* 小尺寸按钮 */
.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
    border-radius: 8px;
}

/* 大尺寸按钮 */
.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
    border-radius: 16px;
}

/* ========== 消息提示 ========== */
.message {
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    font-weight: 500;
}

.success {
    background: rgba(81, 207, 102, 0.2);
    border: 1px solid rgba(81, 207, 102, 0.5);
    color: #51cf66;
}

.error {
    background: rgba(255, 107, 107, 0.2);
    border: 1px solid rgba(255, 107, 107, 0.5);
    color: #ff6b6b;
}

.warning {
    background: rgba(255, 193, 7, 0.2);
    border: 1px solid rgba(255, 193, 7, 0.5);
    color: #ffc107;
}

/* 移除动画效果 */

/* ========== 状态徽章 ========== */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.status-expired {
    background: rgba(255, 107, 107, 0.2);
    color: #ff6b6b;
    border: 1px solid rgba(255, 107, 107, 0.5);
}

.status-banned {
    background: rgba(134, 142, 150, 0.2);
    color: #868e96;
    border: 1px solid rgba(134, 142, 150, 0.5);
}

/* ========== 加载动画 - 简化版本 ========== */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
}

/* ========== 登录页面样式 ========== */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.login-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    width: 100%;
    max-width: 400px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-card h1 {
    color: white;
    text-align: center;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: 600;
}

.login-card .form-group {
    margin-bottom: 20px;
}

.login-card .btn {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    margin-top: 10px;
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 代码编辑器样式 */
.code-editor {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
    font-family: 'Consolas', 'Monaco', monospace;
    color: #f8f8f2;
    font-size: 14px;
    line-height: 1.5;
    min-height: 300px;
    resize: vertical;
}

/* 进度条 */
.progress {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* ========== 自定义弹窗样式 ========== */
/* 弹窗遮罩层 */
.modal-overlay {
    display: none;
}

.modal-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    opacity: 0;
    visibility: hidden;
    z-index: 9999;
    width: 90%;
    max-width: 450px;
    background: rgba(30, 30, 50, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    color: white;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

.modal-dialog.active {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    visibility: visible;
}

/* 弹窗头部 */
.modal-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.modal-icon {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    animation: modal-icon-pulse 1.5s infinite;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
}

.modal-icon.warning {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.modal-icon.danger {
    background: linear-gradient(135deg, #f87171, #ef4444);
}

.modal-icon.success {
    background: linear-gradient(135deg, #4ade80, #22c55e);
}

.modal-icon.info {
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
}

@keyframes modal-icon-pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.9; }
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin: 0;
    flex-grow: 1;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 弹窗内容 */
.modal-body {
    padding: 25px 20px;
}

.modal-message {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.95);
    margin: 0;
}

/* 弹窗按钮区域 */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 15px 20px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
}

.modal-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 22px;
    border: none;
    border-radius: 10px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.modal-btn:hover::before {
    left: 0;
}

.modal-btn-confirm {
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
    color: white;
}

.modal-btn-confirm:hover {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 0 6px 15px rgba(59, 130, 246, 0.4);
}

.modal-btn-cancel {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-btn-cancel:hover {
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.modal-btn-danger {
    background: linear-gradient(135deg, #f87171, #ef4444);
    color: white;
}

.modal-btn-danger:hover {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    box-shadow: 0 6px 15px rgba(239, 68, 68, 0.4);
}

.modal-btn-success {
    background: linear-gradient(135deg, #4ade80, #22c55e);
    color: white;
}

.modal-btn-success:hover {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    box-shadow: 0 6px 15px rgba(34, 197, 94, 0.4);
}

@media (max-width: 768px) {
    .modal-dialog {
        width: 95%;
        max-width: 400px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px;
    }
    
    .modal-footer {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .modal-btn {
        flex: 1;
        min-width: 120px;
    }
}

/* 弹窗动画效果 - 简化版本 */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* ========== 全局毛玻璃效果样式 ========== */

/* 通用卡片样式 - 简化版本 */
.card,
.glass-card,
.panel,
.modal-content,
.alert,
.notification {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

/* 通用按钮毛玻璃效果 */
.btn,
button:not(.nav-tab):not(.sidebar-toggle),
input[type="submit"],
input[type="button"],
.button,
.action-btn {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    color: white !important;
    padding: 12px 20px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
}

/* 移除按钮悬浮效果 */

/* 主要按钮样式 */
.btn-primary,
.btn.btn-primary {
    background: linear-gradient(135deg, #ff6b9d, #c44569) !important;
    border-color: rgba(255, 107, 157, 0.5) !important;
}

.btn-primary:hover,
.btn.btn-primary:hover {
    background: linear-gradient(135deg, #ff7ba7, #d55a7a) !important;
    box-shadow: 0 6px 20px rgba(255, 107, 157, 0.3) !important;
    backdrop-filter: blur(25px) !important;
    -webkit-backdrop-filter: blur(25px) !important;
}

/* 成功按钮样式 */
.btn-success,
.btn.btn-success {
    background: linear-gradient(135deg, #4ade80, #22c55e) !important;
    border-color: rgba(74, 222, 128, 0.5) !important;
}

.btn-success:hover,
.btn.btn-success:hover {
    background: linear-gradient(135deg, #5de88a, #34d568) !important;
    box-shadow: 0 6px 20px rgba(74, 222, 128, 0.3) !important;
    backdrop-filter: blur(25px) !important;
    -webkit-backdrop-filter: blur(25px) !important;
}

/* 危险按钮样式 */
.btn-danger,
.btn.btn-danger {
    background: linear-gradient(135deg, #f87171, #ef4444) !important;
    border-color: rgba(248, 113, 113, 0.5) !important;
}

.btn-danger:hover,
.btn.btn-danger:hover {
    background: linear-gradient(135deg, #f98181, #f56565) !important;
    box-shadow: 0 6px 20px rgba(248, 113, 113, 0.3) !important;
    backdrop-filter: blur(25px) !important;
    -webkit-backdrop-filter: blur(25px) !important;
}

/* 警告按钮样式 */
.btn-warning,
.btn.btn-warning {
    background: linear-gradient(135deg, #fbbf24, #f59e0b) !important;
    border-color: rgba(251, 191, 36, 0.5) !important;
}

.btn-warning:hover,
.btn.btn-warning:hover {
    background: linear-gradient(135deg, #fcd34d, #f59e0b) !important;
    box-shadow: 0 6px 20px rgba(251, 191, 36, 0.3) !important;
    backdrop-filter: blur(25px) !important;
    -webkit-backdrop-filter: blur(25px) !important;
}

/* 次要按钮样式 */
.btn-secondary,
.btn.btn-secondary {
    background: rgba(108, 117, 125, 0.3) !important;
    border-color: rgba(108, 117, 125, 0.5) !important;
}

.btn-secondary:hover,
.btn.btn-secondary:hover {
    background: rgba(108, 117, 125, 0.4) !important;
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3) !important;
    backdrop-filter: blur(25px) !important;
    -webkit-backdrop-filter: blur(25px) !important;
}

/* 信息按钮样式 */
.btn-info,
.btn.btn-info {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
    border-color: rgba(59, 130, 246, 0.5) !important;
}

.btn-info:hover,
.btn.btn-info:hover {
    background: linear-gradient(135deg, #60a5fa, #2563eb) !important;
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3) !important;
    backdrop-filter: blur(25px) !important;
    -webkit-backdrop-filter: blur(25px) !important;
}

/* 表单输入框毛玻璃效果 */
input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
input[type="tel"],
input[type="url"],
textarea,
select,
.form-control,
.input-field {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    color: white !important;
    padding: 12px 15px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

input[type="text"]::placeholder,
input[type="password"]::placeholder,
input[type="email"]::placeholder,
input[type="number"]::placeholder,
input[type="tel"]::placeholder,
input[type="url"]::placeholder,
textarea::placeholder,
.form-control::placeholder,
.input-field::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
textarea:focus,
select:focus,
.form-control:focus,
.input-field:focus {
    outline: none !important;
    border-color: rgba(255, 107, 157, 0.6) !important;
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
}

/* 表格毛玻璃效果 */
.table,
table,
.data-table {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.table th,
table th,
.data-table th {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    color: white !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.table td,
table td,
.data-table td {
    color: rgba(255, 255, 255, 0.9) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.table tr:hover,
table tr:hover,
.data-table tr:hover {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

/* 弹窗毛玻璃效果 */
.modal,
.popup,
.dialog,
.overlay {
    background: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
}

.modal-dialog,
.popup-content,
.dialog-content {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
}

/* 状态标签毛玻璃效果 */
.badge,
.tag,
.status,
.label,
.chip {
    background: rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 12px !important;
    color: white !important;
    padding: 6px 12px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

/* 导航栏毛玻璃效果 */
.navbar,
.nav,
.menu,
.header {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

/* 侧边栏毛玻璃效果 */
.sidebar,
.side-panel {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 4px 0 16px rgba(0, 0, 0, 0.1) !important;
}

/* 面板毛玻璃效果 */
.panel,
.widget,
.box {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 15px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

/* 消息提示毛玻璃效果 */
.alert,
.notification,
.toast,
.message {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    color: white !important;
}

.alert-success,
.notification-success,
.toast-success,
.message-success {
    background: rgba(74, 222, 128, 0.2) !important;
    border-color: rgba(74, 222, 128, 0.3) !important;
    color: #4ade80 !important;
}

.alert-error,
.notification-error,
.toast-error,
.message-error {
    background: rgba(248, 113, 113, 0.2) !important;
    border-color: rgba(248, 113, 113, 0.3) !important;
    color: #f87171 !important;
}

.alert-warning,
.notification-warning,
.toast-warning,
.message-warning {
    background: rgba(251, 191, 36, 0.2) !important;
    border-color: rgba(251, 191, 36, 0.3) !important;
    color: #fbbf24 !important;
}

.alert-info,
.notification-info,
.toast-info,
.message-info {
    background: rgba(59, 130, 246, 0.2) !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
    color: #3b82f6 !important;
}

/* 选项卡毛玻璃效果 */
.tabs,
.tab-nav {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 12px !important;
}

.tab-item,
.tab-link {
    background: transparent !important;
    color: rgba(255, 255, 255, 0.7) !important;
    border: none !important;
    padding: 12px 20px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.tab-item:hover,
.tab-link:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
}

.tab-item.active,
.tab-link.active {
    background: linear-gradient(135deg, #ff6b9d, #c44569) !important;
    color: white !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4) !important;
}

/* 下拉菜单毛玻璃效果 */
.dropdown,
.select-dropdown {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.dropdown-item,
.select-option {
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 10px 15px !important;
    transition: all 0.3s ease !important;
}

/* 移除下拉菜单悬浮效果 */

/* 进度条毛玻璃效果 */
.progress,
.progress-bar {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 20px !important;
    overflow: hidden !important;
}

.progress-fill {
    background: linear-gradient(135deg, #ff6b9d, #c44569) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
}

/* 开关切换毛玻璃效果 */
.switch,
.toggle {
    background: rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 20px !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

.switch:checked,
.toggle:checked {
    background: rgba(74, 222, 128, 0.4) !important;
    border-color: rgba(74, 222, 128, 0.6) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
}

/* 加载动画毛玻璃效果 */
.spinner,
.loader {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    border-top-color: #ff6b9d !important;
    border-radius: 50% !important;
}

/* 工具提示毛玻璃效果 */
.tooltip,
.popover {
    background: rgba(0, 0, 0, 0.8) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    color: white !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .btn,
    button:not(.nav-tab):not(.sidebar-toggle),
    input[type="submit"],
    input[type="button"],
    .button,
    .action-btn {
        padding: 10px 16px !important;
        font-size: 14px !important;
    }
    
    .card,
    .glass-card,
    .panel {
        margin: 10px !important;
        padding: 15px !important;
    }
}

/* 动画效果 */
@keyframes glassShimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.glass-shimmer {
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.1) 100%
    );
    background-size: 200% 100%;
    animation: glassShimmer 2s infinite;
}

/* 禁用状态毛玻璃效果 */
.btn:disabled,
button:disabled,
input:disabled,
.disabled {
    background: rgba(255, 255, 255, 0.05) !important;
    color: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
    cursor: not-allowed !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

.btn:disabled:hover,
button:disabled:hover,
input:disabled:hover,
.disabled:hover {
    transform: none !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}