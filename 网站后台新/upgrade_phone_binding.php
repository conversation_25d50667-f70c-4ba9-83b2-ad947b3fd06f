<?php
/**
 * 数据库升级脚本：添加手机号码绑定功能
 * 版本：v1.0.14
 * 日期：2025-08-21
 */

header('Content-Type: text/html; charset=utf-8');

// 引入数据库配置
require_once 'includes/db.php';

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>数据库升级 - 手机号码绑定功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 数据库升级：手机号码绑定功能</h1>
        <p>正在为小梅花AI智能客服系统添加手机号码绑定功能...</p>";

try {
    // 检查数据库连接
    echo "<div class='step'><h3>步骤1: 检查数据库连接</h3>";
    $pdo->query("SELECT 1");
    echo "<div class='success'>✅ 数据库连接正常</div></div>";

    // 检查license_keys表是否存在
    echo "<div class='step'><h3>步骤2: 检查license_keys表</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'license_keys'");
    if ($stmt->rowCount() == 0) {
        throw new Exception("license_keys表不存在，请先运行基础数据库安装脚本");
    }
    echo "<div class='success'>✅ license_keys表存在</div></div>";

    // 检查phone_number字段是否已存在
    echo "<div class='step'><h3>步骤3: 检查phone_number字段</h3>";
    $stmt = $pdo->query("SHOW COLUMNS FROM license_keys LIKE 'phone_number'");
    $phone_field_exists = $stmt->rowCount() > 0;
    
    if ($phone_field_exists) {
        echo "<div class='warning'>⚠️ phone_number字段已存在，跳过添加</div>";
    } else {
        echo "<div class='info'>📝 phone_number字段不存在，准备添加...</div>";
        
        // 添加phone_number字段
        $sql = "ALTER TABLE license_keys ADD COLUMN phone_number varchar(20) DEFAULT NULL COMMENT '绑定的手机号码' AFTER user_wechat";
        $pdo->exec($sql);
        echo "<div class='success'>✅ 成功添加phone_number字段</div>";
    }
    echo "</div>";

    // 检查并添加索引
    echo "<div class='step'><h3>步骤4: 检查phone_number索引</h3>";
    $stmt = $pdo->query("SHOW INDEX FROM license_keys WHERE Key_name = 'idx_phone_number'");
    $index_exists = $stmt->rowCount() > 0;
    
    if ($index_exists) {
        echo "<div class='warning'>⚠️ idx_phone_number索引已存在，跳过添加</div>";
    } else {
        echo "<div class='info'>📝 idx_phone_number索引不存在，准备添加...</div>";
        
        // 添加索引
        $sql = "ALTER TABLE license_keys ADD INDEX idx_phone_number (phone_number)";
        $pdo->exec($sql);
        echo "<div class='success'>✅ 成功添加idx_phone_number索引</div>";
    }
    echo "</div>";

    // 创建手机号码登录日志表
    echo "<div class='step'><h3>步骤5: 创建手机号码登录日志表</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'phone_login_logs'");
    $table_exists = $stmt->rowCount() > 0;
    
    if ($table_exists) {
        echo "<div class='warning'>⚠️ phone_login_logs表已存在，跳过创建</div>";
    } else {
        echo "<div class='info'>📝 phone_login_logs表不存在，准备创建...</div>";
        
        $sql = "CREATE TABLE `phone_login_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `phone_number` varchar(20) NOT NULL COMMENT '登录的手机号码',
            `license_key_id` int(11) NOT NULL COMMENT '关联的卡密ID',
            `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
            `ip_address` varchar(45) DEFAULT NULL COMMENT '登录IP地址',
            `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
            `login_result` enum('success','failed') NOT NULL DEFAULT 'success' COMMENT '登录结果',
            `error_message` varchar(255) DEFAULT NULL COMMENT '错误信息',
            PRIMARY KEY (`id`),
            KEY `idx_phone_number` (`phone_number`),
            KEY `idx_license_key_id` (`license_key_id`),
            KEY `idx_login_time` (`login_time`),
            FOREIGN KEY (`license_key_id`) REFERENCES `license_keys` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手机号码登录日志表'";
        
        $pdo->exec($sql);
        echo "<div class='success'>✅ 成功创建phone_login_logs表</div>";
    }
    echo "</div>";

    // 验证升级结果
    echo "<div class='step'><h3>步骤6: 验证升级结果</h3>";
    
    // 检查字段
    $stmt = $pdo->query("DESCRIBE license_keys phone_number");
    $field_info = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<div class='info'>📋 phone_number字段信息：</div>";
    echo "<pre>" . print_r($field_info, true) . "</pre>";
    
    // 检查索引
    $stmt = $pdo->query("SHOW INDEX FROM license_keys WHERE Key_name = 'idx_phone_number'");
    $index_info = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<div class='info'>📋 idx_phone_number索引信息：</div>";
    echo "<pre>" . print_r($index_info, true) . "</pre>";
    
    // 检查表
    $stmt = $pdo->query("DESCRIBE phone_login_logs");
    $table_structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<div class='info'>📋 phone_login_logs表结构：</div>";
    echo "<pre>" . print_r($table_structure, true) . "</pre>";
    
    echo "</div>";

    echo "<div class='success'>
        <h2>🎉 数据库升级完成！</h2>
        <p>手机号码绑定功能的数据库结构已成功添加：</p>
        <ul>
            <li>✅ license_keys表已添加phone_number字段</li>
            <li>✅ 已创建phone_number字段索引</li>
            <li>✅ 已创建phone_login_logs登录日志表</li>
        </ul>
        <p><strong>下一步：</strong>请继续进行APP和网站后台的功能开发。</p>
    </div>";

} catch (Exception $e) {
    echo "<div class='error'>
        <h3>❌ 升级失败</h3>
        <p><strong>错误信息：</strong>" . htmlspecialchars($e->getMessage()) . "</p>
        <p><strong>文件：</strong>" . $e->getFile() . "</p>
        <p><strong>行号：</strong>" . $e->getLine() . "</p>
    </div>";
}

echo "
    </div>
</body>
</html>";
?>
