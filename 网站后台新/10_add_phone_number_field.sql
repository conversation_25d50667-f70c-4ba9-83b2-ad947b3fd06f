-- ====================================
-- 数据库升级脚本：添加手机号码绑定功能
-- 版本：v1.0.14
-- 日期：2025-08-21
-- 功能：为卡密添加手机号码绑定功能
-- ====================================

-- 检查并添加phone_number字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'license_keys'
    AND COLUMN_NAME = 'phone_number'
);

-- 如果字段不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE license_keys ADD COLUMN phone_number varchar(20) DEFAULT NULL COMMENT "绑定的手机号码" AFTER user_wechat',
    'SELECT "phone_number字段已存在，跳过添加" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加phone_number索引
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'license_keys'
    AND INDEX_NAME = 'idx_phone_number'
);

-- 如果索引不存在，则添加
SET @sql = IF(@index_exists = 0,
    'ALTER TABLE license_keys ADD INDEX idx_phone_number (phone_number)',
    'SELECT "idx_phone_number索引已存在，跳过添加" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建手机号码登录日志表
CREATE TABLE IF NOT EXISTS `phone_login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `phone_number` varchar(20) NOT NULL COMMENT '登录的手机号码',
  `license_key_id` int(11) NOT NULL COMMENT '关联的卡密ID',
  `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '登录IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `login_result` enum('success','failed') NOT NULL DEFAULT 'success' COMMENT '登录结果',
  `error_message` varchar(255) DEFAULT NULL COMMENT '错误信息',
  PRIMARY KEY (`id`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_license_key_id` (`license_key_id`),
  KEY `idx_login_time` (`login_time`),
  FOREIGN KEY (`license_key_id`) REFERENCES `license_keys` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手机号码登录日志表';

-- 显示升级完成信息
SELECT 
    '数据库升级完成' as status,
    '已添加phone_number字段和相关索引' as message,
    '已创建phone_login_logs表' as additional_info,
    NOW() as upgrade_time;
