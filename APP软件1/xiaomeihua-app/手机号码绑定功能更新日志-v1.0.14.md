# 小梅花AI智能客服 v1.0.14 更新日志

## 🎉 新功能：手机号码绑定登录

### 📱 主要功能更新

#### 1. 卡密绑定手机号码功能
- **强制绑定**：未绑定手机号码的卡密在登录时自动显示手机号码输入框
- **安全验证**：必须绑定手机号码才能进入APP，确保账户安全
- **唯一性保证**：一个手机号码只能绑定一个卡密，防止重复绑定

#### 2. 手机号码登录功能
- **双重登录方式**：支持卡密登录和手机号码登录两种方式
- **便捷切换**：登录界面提供登录方式切换按钮
- **自动识别**：系统自动识别已绑定手机号码的卡密

#### 3. APP设置页面优化
- **信息展示**：在设置页面显示绑定的手机号码（脱敏显示）
- **安全提示**：明确标注手机号码不可更改，需联系管理员修改
- **状态显示**：清晰显示手机号码绑定状态

#### 4. 网站后台管理优化
- **手机号码列**：卡密管理页面新增手机号码显示列
- **脱敏显示**：手机号码以脱敏形式显示（如：138****5678）
- **管理功能**：管理员可以修改卡密绑定的手机号码
- **状态标识**：清晰标识已绑定和未绑定状态

### 🔧 技术改进

#### 数据库优化
- 新增 `phone_number` 字段到 `license_keys` 表
- 创建手机号码索引，提升查询性能
- 新增 `phone_login_logs` 表记录手机号码登录日志

#### API增强
- 优化验证API，支持手机号码绑定和登录
- 增强错误处理，提供详细的错误信息
- 添加手机号码格式验证和重复检查

#### 安全性提升
- 手机号码格式严格验证（11位中国大陆手机号）
- 防止手机号码重复绑定
- 登录日志记录，便于安全审计

### 📋 使用说明

#### 首次使用卡密登录
1. 输入卡密
2. 系统检测到未绑定手机号码，自动显示手机号码输入框
3. 输入11位手机号码完成绑定
4. 绑定成功后进入系统

#### 使用手机号码登录
1. 在登录界面点击"手机号登录"
2. 输入已绑定的手机号码
3. 系统验证通过后自动登录

#### 查看绑定信息
1. 进入APP设置页面
2. 在"卡密信息"区域查看绑定的手机号码
3. 手机号码以脱敏形式显示，确保隐私安全

### 🛠️ 管理员功能

#### 网站后台管理
1. 登录网站后台管理系统
2. 进入"卡密管理"页面
3. 查看所有卡密的手机号码绑定状态
4. 可以修改卡密绑定的手机号码

#### 数据库升级
1. 运行 `upgrade_phone_binding.php` 脚本
2. 自动添加必要的数据库字段和索引
3. 创建手机号码登录日志表

### 🔄 版本兼容性
- 完全向后兼容，现有卡密正常使用
- 未绑定手机号码的卡密会提示绑定
- 已绑定手机号码的卡密可以使用两种登录方式

### 📝 注意事项
1. **手机号码唯一性**：每个手机号码只能绑定一个卡密
2. **格式要求**：仅支持中国大陆11位手机号码
3. **修改限制**：用户无法自行修改绑定的手机号码
4. **管理员权限**：只有管理员可以在后台修改手机号码绑定

### 🚀 下一步计划
- 支持短信验证码登录
- 增加手机号码绑定历史记录
- 支持国际手机号码格式
- 添加手机号码绑定通知功能

---

**版本号：** v1.0.14  
**发布日期：** 2025年8月21日  
**开发团队：** 小梅花AI科技  

如有问题或建议，请联系技术支持团队。
