#!/usr/bin/env node

/**
 * 小梅花AI智能客服 v1.0.14 手机号码绑定版本构建脚本
 * 功能：构建支持手机号码绑定登录的DMG安装包
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建小梅花AI智能客服 v1.0.14 (手机号码绑定版本)');
console.log('=' .repeat(60));

// 检查环境
function checkEnvironment() {
    console.log('📋 检查构建环境...');
    
    try {
        // 检查Node.js版本
        const nodeVersion = process.version;
        console.log(`✅ Node.js版本: ${nodeVersion}`);
        
        // 检查npm版本
        const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
        console.log(`✅ npm版本: ${npmVersion}`);
        
        // 检查electron-builder
        try {
            const electronBuilderVersion = execSync('npx electron-builder --version', { encoding: 'utf8' }).trim();
            console.log(`✅ electron-builder版本: ${electronBuilderVersion}`);
        } catch (e) {
            console.log('⚠️  electron-builder未安装，将自动安装');
        }
        
        // 检查package.json
        const packagePath = path.join(__dirname, 'package.json');
        if (fs.existsSync(packagePath)) {
            const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            console.log(`✅ 应用版本: ${packageJson.version}`);
            console.log(`✅ 应用名称: ${packageJson.productName}`);
        } else {
            throw new Error('package.json文件不存在');
        }
        
        console.log('✅ 环境检查完成\n');
        return true;
    } catch (error) {
        console.error('❌ 环境检查失败:', error.message);
        return false;
    }
}

// 清理构建目录
function cleanBuildDirectory() {
    console.log('🧹 清理构建目录...');
    
    try {
        execSync('npm run clean', { stdio: 'inherit' });
        console.log('✅ 构建目录清理完成\n');
        return true;
    } catch (error) {
        console.error('❌ 清理构建目录失败:', error.message);
        return false;
    }
}

// 安装依赖
function installDependencies() {
    console.log('📦 检查并安装依赖...');
    
    try {
        execSync('npm install', { stdio: 'inherit' });
        console.log('✅ 依赖安装完成\n');
        return true;
    } catch (error) {
        console.error('❌ 依赖安装失败:', error.message);
        return false;
    }
}

// 验证配置
function validateConfiguration() {
    console.log('🔍 验证构建配置...');
    
    try {
        execSync('node scripts/validate-config.js', { stdio: 'inherit' });
        console.log('✅ 配置验证完成\n');
        return true;
    } catch (error) {
        console.error('❌ 配置验证失败:', error.message);
        return false;
    }
}

// 构建应用
function buildApplication() {
    console.log('🔨 构建应用程序...');
    console.log('⏳ 这可能需要几分钟时间，请耐心等待...\n');
    
    try {
        // 构建macOS版本（支持Apple Silicon和Intel）
        execSync('npm run build:all', { stdio: 'inherit' });
        console.log('✅ 应用构建完成\n');
        return true;
    } catch (error) {
        console.error('❌ 应用构建失败:', error.message);
        return false;
    }
}

// 验证构建结果
function verifyBuildResults() {
    console.log('🔍 验证构建结果...');
    
    try {
        const distPath = path.join(__dirname, 'dist');
        if (!fs.existsSync(distPath)) {
            throw new Error('dist目录不存在');
        }
        
        const files = fs.readdirSync(distPath);
        console.log('📁 构建输出文件:');
        
        let dmgCount = 0;
        files.forEach(file => {
            console.log(`   - ${file}`);
            if (file.endsWith('.dmg')) {
                dmgCount++;
                const filePath = path.join(distPath, file);
                const stats = fs.statSync(filePath);
                const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
                console.log(`     大小: ${sizeInMB} MB`);
            }
        });
        
        if (dmgCount === 0) {
            throw new Error('未找到DMG安装包');
        }
        
        console.log(`✅ 成功生成 ${dmgCount} 个DMG安装包\n`);
        return true;
    } catch (error) {
        console.error('❌ 构建结果验证失败:', error.message);
        return false;
    }
}

// 生成构建报告
function generateBuildReport() {
    console.log('📊 生成构建报告...');
    
    try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        const buildTime = new Date().toLocaleString('zh-CN');

        let report = `# 小梅花AI智能客服 v${packageJson.version} 构建报告

## 构建信息
- **版本号**: ${packageJson.version}
- **构建时间**: ${buildTime}
- **构建类型**: 手机号码绑定功能版本
- **支持平台**: macOS (Apple Silicon + Intel)

## 新功能特性
- ✅ 卡密绑定手机号码功能
- ✅ 手机号码登录支持
- ✅ APP设置页面显示手机号码
- ✅ 网站后台手机号码管理
- ✅ 数据库结构优化

## 构建输出
`;

        const distPath = path.join(__dirname, 'dist');
        if (fs.existsSync(distPath)) {
            const files = fs.readdirSync(distPath);
            files.forEach(file => {
                if (file.endsWith('.dmg')) {
                    const filePath = path.join(distPath, file);
                    const stats = fs.statSync(filePath);
                    const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
                    report += `- **${file}**: ${sizeInMB} MB\n`;
                }
            });
        }
        
        const reportPath = path.join(__dirname, `构建报告-v${packageJson.version}.md`);
        fs.writeFileSync(reportPath, report + '\n## 安装说明\n1. 下载对应架构的DMG文件\n2. 双击DMG文件进行安装\n3. 首次使用需要绑定手机号码\n\n构建完成时间: ' + buildTime);
        
        console.log(`✅ 构建报告已生成: ${reportPath}\n`);
        return true;
    } catch (error) {
        console.error('❌ 生成构建报告失败:', error.message);
        return false;
    }
}

// 主构建流程
async function main() {
    console.log('🎯 开始执行构建流程...\n');
    
    const steps = [
        { name: '环境检查', func: checkEnvironment },
        { name: '清理构建目录', func: cleanBuildDirectory },
        { name: '安装依赖', func: installDependencies },
        { name: '验证配置', func: validateConfiguration },
        { name: '构建应用', func: buildApplication },
        { name: '验证构建结果', func: verifyBuildResults },
        { name: '生成构建报告', func: generateBuildReport }
    ];
    
    for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        console.log(`📍 步骤 ${i + 1}/${steps.length}: ${step.name}`);
        
        const success = step.func();
        if (!success) {
            console.error(`❌ 构建失败于步骤: ${step.name}`);
            process.exit(1);
        }
    }
    
    console.log('🎉 构建完成！');
    console.log('=' .repeat(60));
    console.log('📦 DMG安装包已生成到 dist/ 目录');
    console.log('📋 请查看构建报告了解详细信息');
    console.log('🚀 小梅花AI智能客服 v1.0.14 (手机号码绑定版本) 构建成功！');
}

// 执行构建
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 构建过程发生未知错误:', error);
        process.exit(1);
    });
}

module.exports = { main };
